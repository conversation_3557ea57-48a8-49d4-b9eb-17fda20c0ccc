# Stripe API Key Mismatch Fix

## Problem Summary

You're experiencing a Stripe API key mismatch where:
- **Backend** is using a **live** secret key (`sk_live_...`)
- **Frontend** is using a **test** public key (`pk_test_...`)

This causes the error: "No such payment_intent: 'pi_xxx'; a similar object exists in live mode, but a test mode key was used to make this request."

## Root Cause

The payment intent is created in **live mode** (backend) but the frontend tries to confirm it in **test mode**, creating a cross-mode incompatibility.

## Solution Steps

### Step 1: Identify Current Key Configuration

Run your application and check the console logs. You should see debug output like:

```
🔍 Stripe Key Analysis - Backend PaymentsService
==================================================
Backend Secret Key: sk_live... (live mode)

🔍 Stripe Key Analysis - Frontend Checkout
==================================================
Frontend Public Key: pk_test... (test mode)
❌ Key compatibility issues:
   - Mode mismatch: Backend is using live mode, Frontend is using test mode
```

### Step 2: Choose Your Target Mode

**For Development/Testing:**
- Use test keys for both backend and frontend
- Recommended for development environment

**For Production:**
- Use live keys for both backend and frontend
- Only use when ready for real payments

### Step 3: Update Environment Variables

#### Option A: Switch to Test Mode (Recommended for Development)

**Backend (.env):**
```bash
STRIPE_SECRET_KEY=sk_test_your_test_secret_key_here
```

**Frontend (.env.local):**
```bash
NEXT_PUBLIC_STRIPE_PUBLIC_KEY=pk_test_your_test_public_key_here
```

#### Option B: Switch to Live Mode (Production Only)

**Backend (.env):**
```bash
STRIPE_SECRET_KEY=sk_live_your_live_secret_key_here
```

**Frontend (.env.local):**
```bash
NEXT_PUBLIC_STRIPE_PUBLIC_KEY=pk_live_your_live_public_key_here
```

### Step 4: Verify Key Pairs

Ensure your keys are from the same Stripe account and mode:

1. Go to [Stripe Dashboard](https://dashboard.stripe.com/apikeys)
2. For **Test Mode**: Use keys from the "Test mode" section
3. For **Live Mode**: Use keys from the "Live mode" section

**Test Mode Keys:**
- Secret: `sk_test_...`
- Publishable: `pk_test_...`

**Live Mode Keys:**
- Secret: `sk_live_...`
- Publishable: `pk_live_...`

### Step 5: Restart Services

After updating environment variables:

1. **Restart Backend Server:**
   ```bash
   # Stop and restart your NestJS backend
   npm run start:dev
   ```

2. **Restart Frontend Server:**
   ```bash
   # Stop and restart your Next.js frontend
   npm run dev
   ```

### Step 6: Test Payment Flow

1. Add items to cart
2. Go to checkout page
3. Check console logs for key compatibility
4. Try to process a test payment
5. Use Stripe test cards (e.g., `****************`)

## Environment Variable Checklist

### Backend Environment Variables
- [ ] `STRIPE_SECRET_KEY` is set
- [ ] Key starts with `sk_test_` (test) or `sk_live_` (live)
- [ ] Key is from the correct Stripe account

### Frontend Environment Variables
- [ ] `NEXT_PUBLIC_STRIPE_PUBLIC_KEY` is set
- [ ] Key starts with `pk_test_` (test) or `pk_live_` (live)
- [ ] Key matches the same mode as backend

### Webhook Configuration (if using webhooks)
- [ ] `STRIPE_WEBHOOK_SECRET` is set
- [ ] Webhook endpoint is configured in Stripe dashboard
- [ ] Webhook is set to the same mode as your keys

## Common Issues

### Issue 1: Environment Variables Not Loading
**Solution:** Ensure `.env` files are in the correct location and restart servers.

### Issue 2: Mixed Test/Live Keys
**Solution:** Double-check that both backend and frontend use the same mode (test or live).

### Issue 3: Wrong Stripe Account
**Solution:** Verify all keys are from the same Stripe account.

## Debugging Commands

### Check Environment Variables

**Backend:**
```bash
# In your backend directory
echo $STRIPE_SECRET_KEY
```

**Frontend:**
```bash
# In your frontend directory
echo $NEXT_PUBLIC_STRIPE_PUBLIC_KEY
```

### Test Stripe Connection

Use the debug utility we added:
```typescript
import { logStripeKeyAnalysis } from '@/lib/stripe-debug';

// This will log key compatibility analysis
logStripeKeyAnalysis(
  'Manual Check',
  process.env.STRIPE_SECRET_KEY,
  process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY
);
```

## Files Modified for Debugging

1. `api.myyresearch.com/src/app/payments/payments.service.ts` - Added backend key logging
2. `myyresearch.com/app/(website)/checkout/page.tsx` - Added frontend key logging
3. `myyresearch.com/app/webhooks/stripe/route.ts` - Added webhook key logging
4. `myyresearch.com/lib/stripe-debug.ts` - Debug utility (NEW)

## Next Steps

1. **Immediate Fix:** Update environment variables to use matching test keys
2. **Verify Fix:** Check console logs show compatible keys
3. **Test Payment:** Process a test payment end-to-end
4. **Remove Debug Logs:** Once fixed, remove debug logging from production code
5. **Production Setup:** When ready, switch to live keys for production deployment

## Test Cards for Development

When using test mode, use these Stripe test cards:

- **Successful Payment:** `****************`
- **Declined Payment:** `****************`
- **Insufficient Funds:** `****************`

All test cards use any future expiry date and any 3-digit CVC.
