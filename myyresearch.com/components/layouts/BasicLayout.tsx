"use client";
import { useState, useEffect } from "react";
import { motion, useScroll } from "framer-motion";
import Footer from "./footer";
import Navigation from "./navigation";
import CrispChat from "@/components/crispy-chat";

export function BasicLayout(props: { children: React.ReactNode }) {
  const [isVisible, setIsVisible] = useState(true);
  const { scrollY } = useScroll();

  useEffect(() => {
    let lastScrollY = 0;

    const updateNavVisibility = () => {
      const currentScrollY = scrollY.get();
      setIsVisible(currentScrollY <= lastScrollY || currentScrollY < 50);
      lastScrollY = currentScrollY;
    };

    const unsubscribe = scrollY.onChange(updateNavVisibility);
    return () => unsubscribe();
  }, [scrollY]);

  return (
    <div className="bg-white">
      <motion.div
        initial={{ top: 0 }}
        // animate={{ top: isVisible ? 0 : -80 }}
        // transition={{ duration: 0.3 }}
        //@ts-ignore
        className="fixed left-0 right-0 z-50"
      >
        <Navigation />
      </motion.div>
      <main className="isolate pt-[80px]">{props.children}</main>
      <CrispChat />
      <Footer />
    </div>
  );
}
