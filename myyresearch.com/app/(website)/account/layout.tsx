"use client";

import { useContext, useEffect } from "react";
import Link from "next/link";
import { redirect, usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { User, ShoppingBag, CreditCard, Star } from "lucide-react";
import { AuthContext } from "@/contexts/auth-contexts";
import { Role } from "@/types/auth";

const getTabItems = (userRole?: Role) => {
  const baseItems = [
    {
      title: "Resources",
      href: "/account/resources",
      icon: Star,
    },
    {
      title: "Orders",
      href: "/account/orders",
      icon: ShoppingBag,
    },
  ];

  // Add payment details tab for instructors
  if (userRole === Role.Instructor) {
    baseItems.push({
      title: "Payment Details",
      href: "/account/payment-details",
      icon: CreditCard,
    });
  }

  return baseItems;
};

const Layout = ({ children }: { children: React.ReactNode }) => {
  const pathname = usePathname();
  const auth = useContext(AuthContext);

  useEffect(() => {
    // Only redirect when auth loading is complete and user is not authenticated
    if (!auth?.loading && !auth?.user) {
      redirect("/auth/sign-in?redirect=/account");
    }
  }, [auth?.loading, auth?.user]);

  // Show loading spinner while auth is loading
  if (auth?.loading) {
    return (
      <div className="flex min-h-svh items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
      </div>
    );
  }

  // If auth is complete but no user, the redirect should have happened
  // This is a fallback in case redirect doesn't work immediately
  if (!auth?.user) {
    return (
      <div className="flex min-h-svh items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
      </div>
    );
  }

  const tabItems = getTabItems(auth.user.role);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto">
        {/* Header */}
        <div className="bg-white border-b">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <h1 className="text-xl font-semibold text-gray-900">
                My Account
              </h1>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white border-b">
          <div className="px-4 sm:px-6 lg:px-8">
            <nav className="flex space-x-8" aria-label="Tabs">
              {tabItems.map((item) => {
                const Icon = item.icon;
                const isActive = pathname === item.href;

                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      "flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors",
                      isActive
                        ? "border-blue-600 text-blue-600"
                        : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                    )}
                  >
                    <Icon className="mr-2 h-4 w-4" />
                    {item.title}
                  </Link>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Page content */}
        <main className="px-4 sm:px-6 lg:px-8 py-8">{children}</main>
      </div>
    </div>
  );
};

export default Layout;
