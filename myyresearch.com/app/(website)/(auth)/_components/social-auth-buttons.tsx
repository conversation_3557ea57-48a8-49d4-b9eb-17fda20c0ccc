"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Icons } from "@/components/icons";
import { googleAuth, facebookAuth, githubAuth } from "@/lib/social-auth";

export function SocialAuthButtons() {
  return (
    <div className="grid gap-2">
      <Button
        variant="outline"
        className="bg-[#D04A3A] text-white hover:bg-[#B1402F] hover:text-white
        dark:bg-[#D04A3A] dark:text-white dark:hover:bg-[#B1402F] dark:hover:text-white
        flex items-center justify-center gap-2 shadow-[0_4px_7px_rgba(0,0,0,0.1)]
        hover:shadow-[0_4px_10px_rgba(0,0,0,0.15)] transition-all duration-200"
        onClick={(e) => {
          e.preventDefault();
          googleAuth();
        }}
        disabled={false}
      >
        <Icons.google className="mr-2 size-4" />
        Continue with Google
      </Button>

      {/* <Button
        variant="outline"
        className="bg-[#1877F2] text-white hover:bg-[#0C63D4] hover:text-white
        dark:bg-[#1877F2] dark:text-white dark:hover:bg-[#0C63D4] dark:hover:text-white"
        onClick={(e) => {
          e.preventDefault();
          facebookAuth();
        }}
        disabled={true}
      >
        <Icons.facebook className="mr-2 size-4" />
        Continue with Facebook
      </Button> */}

      {/* <Button
        variant="outline"
        className="bg-[#24292F] text-white hover:bg-[#1C2024] hover:text-white
        dark:bg-[#24292F] dark:text-white dark:hover:bg-[#1C2024] dark:hover:text-white"
        onClick={(e) => {
          e.preventDefault();
          githubAuth();
        }}
      >
        <Icons.github className="mr-2 size-4" />
        Continue with GitHub
      </Button> */}
    </div>
  );
}
