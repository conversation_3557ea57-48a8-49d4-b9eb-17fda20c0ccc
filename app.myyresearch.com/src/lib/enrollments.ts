import { ApiResponse } from "@/types/common";
import {
  EnrollmentFilterParams,
  PaginatedEnrollmentsResponse,
} from "@/types/enrollment";
import axios from "@/utils/axios";

// API Function
export async function getAdminEnrollments(
  params: EnrollmentFilterParams
): Promise<ApiResponse<PaginatedEnrollmentsResponse>> {
  try {
    const res = await axios.get("/enrollments/admin", { params });
    return res.data;
  } catch (error) {
    return error as ApiResponse<PaginatedEnrollmentsResponse>;
  }
}
