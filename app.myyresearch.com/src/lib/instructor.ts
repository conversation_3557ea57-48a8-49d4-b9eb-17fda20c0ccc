// import { ApiResponse } from "@/types/common";
// import {
//   InstructorQueryParams,
//   PaginatedInstructorsResponse,
// } from "@/types/instructor";
// import axios from "@/utils/axios";

// export async function getInstructors(
//   params: InstructorQueryParams
// ): Promise<ApiResponse<PaginatedInstructorsResponse>> {
//   try {
//     const res = await axios.get("/instructors/admin", { params });
//     return res.data;
//   } catch (error) {
//     return error as ApiResponse<PaginatedInstructorsResponse>;
//   }
// }
