import * as React from "react";
import Link from "next/link";
import { type SearchParams } from "@/types";

import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  B<PERSON>crumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Skeleton } from "@/components/ui/skeleton";
import { Shell } from "@/components/shell";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

import { InstructorPaymentBalancesTable } from "./_components/instructor-payment-balances-table";
import { PaymentSummaryCards } from "./_components/payment-summary-cards";
import { PaymentRequestsTable } from "./_components/payment-requests-table";
import { PaymentHistoryTable } from "./_components/payment-history-table";
import {
  getInstructorBalances,
  getPaymentSummaryStats,
  getPaymentRequests,
  getPaymentHistory,
} from "./_lib/queries";
import { searchParamsCache } from "./_lib/validations";
import { type Metadata } from "next";

export const metadata: Metadata = {
  title: "Instructor Payments",
  description: "Manage instructor payments and balances",
};

interface InstructorPaymentsPageProps {
  searchParams: Promise<SearchParams>;
}

export default async function InstructorPaymentsPage({
  searchParams,
}: InstructorPaymentsPageProps) {
  const search = searchParamsCache.parse(await searchParams);

  const requestsResponse = await getPaymentRequests();

  return (
    <ContentLayout title="Instructor Payments">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/admin">Dashboard</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Instructor Payments</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <Shell className="gap-4">
        <PaymentRequestsTable requests={requestsResponse} />
      </Shell>
    </ContentLayout>
  );
}
