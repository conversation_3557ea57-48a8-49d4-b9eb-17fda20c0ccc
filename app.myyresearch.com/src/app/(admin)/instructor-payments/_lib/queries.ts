import { unstable_noStore as noStore } from "next/cache";
import {
  getAdminInstructorBalances,
  getAdminPaymentSummaryStats,
  getAdminPaymentRequests,
  getAdminPaymentHistory,
} from "@/lib/instructor-payments";
import type { GetInstructorBalancesSchema } from "./validations";
import type {
  GetInstructorBalancesResponse,
  PaymentSummaryStats,
  PaymentRequest,
  PaymentRecord,
} from "@/types/instructor-payments";
import { ApiStatus } from "@/types/common";

export async function getInstructorBalances(
  input: GetInstructorBalancesSchema
): Promise<GetInstructorBalancesResponse> {
  noStore();

  try {
    const response = await getAdminInstructorBalances({
      search: input.search || undefined,
      minBalance: input.minBalance || undefined,
      maxBalance: input.maxBalance || undefined,
      fromDate: input.fromDate || undefined,
      toDate: input.toDate || undefined,
      page: input.page,
      limit: input.per_page,
    });

    console.log("response getInstructorBalances  :::  ", response);

    if (response.status !== ApiStatus.SUCCESS) {
      throw new Error(
        response.message || "Failed to fetch instructor balances"
      );
    }

    return response.data;
  } catch (error) {
    console.error("Failed to fetch instructor balances:", error);
    return {
      instructors: [],
      totalInstructors: 0,
      totalUnpaidAmount: 0,
      currency: "usd",
    };
  }
}

export async function getPaymentSummaryStats(): Promise<PaymentSummaryStats> {
  noStore();

  try {
    const response = await getAdminPaymentSummaryStats();

    if (response.status !== ApiStatus.SUCCESS) {
      throw new Error(
        response.message || "Failed to fetch payment summary stats"
      );
    }

    return response.data;
  } catch (error) {
    console.error("Failed to fetch payment summary stats:", error);
    return {
      totalEarningsAllTime: 0,
      totalPaidAllTime: 0,
      totalUnpaidAmount: 0,
      totalInstructorsWithEarnings: 0,
      totalInstructorsWithUnpaidBalance: 0,
      currency: "usd",
      averageUnpaidBalance: 0,
      highestUnpaidBalance: 0,
    };
  }
}

export async function getPaymentRequests(): Promise<PaymentRequest[]> {
  noStore();

  try {
    const response = await getAdminPaymentRequests();

    if (response.status !== ApiStatus.SUCCESS) {
      throw new Error(response.message || "Failed to fetch payment requests");
    }

    return response.data;
  } catch (error) {
    console.error("Failed to fetch payment requests:", error);
    return [];
  }
}

export async function getPaymentHistory(): Promise<PaymentRecord[]> {
  noStore();

  try {
    const response = await getAdminPaymentHistory();

    if (response.status !== ApiStatus.SUCCESS) {
      throw new Error(response.message || "Failed to fetch payment history");
    }

    return response.data;
  } catch (error) {
    console.error("Failed to fetch payment history:", error);
    return [];
  }
}
