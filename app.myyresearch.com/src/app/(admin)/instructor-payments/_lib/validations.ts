import { z } from "zod";
import {
  createSearchParamsCache,
  parseAsInteger,
  parseAsString,
} from "nuqs/server";

// Search params schema for instructor payments page
export const searchParamsSchema = z.object({
  page: z.coerce.number().default(1),
  per_page: z.coerce.number().default(10),
  sort: z.string().optional(),
  search: z.string().optional(),
  minBalance: z.coerce.number().optional(),
  maxBalance: z.coerce.number().optional(),
  fromDate: z.string().optional(),
  toDate: z.string().optional(),
});

export type GetInstructorBalancesSchema = z.infer<typeof searchParamsSchema>;

export const searchParamsCache = createSearchParamsCache({
  page: parseAsInteger.withDefault(1),
  per_page: parseAsInteger.withDefault(10),
  sort: parseAsString.withDefault(""),
  search: parseAsString.withDefault(""),
  minBalance: parseAsInteger.withDefault(0),
  maxBalance: parseAsInteger.withDefault(0),
  fromDate: parseAsString.withDefault(""),
  toDate: parseAsString.withDefault(""),
});

// Payment record creation schema
export const createPaymentRecordSchema = z.object({
  instructorId: z.string().min(1, "Instructor ID is required"),
  amount: z.number().min(0.01, "Amount must be greater than 0"),
  currency: z.string().default("usd"),
  paymentMethod: z.enum(["paypal", "bank_transfer", "stripe"]),
  paymentDate: z.string().min(1, "Payment date is required"),
  transactionId: z.string().optional(),
  notes: z.string().optional(),
  orderItemDetailsIds: z
    .array(z.string())
    .min(1, "At least one order item is required"),
});

export type CreatePaymentRecordSchema = z.infer<
  typeof createPaymentRecordSchema
>;

// Payment request update schema
export const updatePaymentRequestSchema = z.object({
  status: z.enum(["pending", "approved", "rejected", "paid"]),
  adminNotes: z.string().optional(),
});

export type UpdatePaymentRequestSchema = z.infer<
  typeof updatePaymentRequestSchema
>;

// Filter schema for instructor balances
export const instructorBalanceFilterSchema = z.object({
  search: z.string().optional(),
  minBalance: z.number().optional(),
  maxBalance: z.number().optional(),
  fromDate: z.string().optional(),
  toDate: z.string().optional(),
  page: z.number().default(1),
  limit: z.number().default(20),
});

export type InstructorBalanceFilterSchema = z.infer<
  typeof instructorBalanceFilterSchema
>;
