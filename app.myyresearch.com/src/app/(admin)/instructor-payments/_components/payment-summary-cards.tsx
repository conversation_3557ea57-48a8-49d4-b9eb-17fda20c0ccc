"use client";

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils";
import { PaymentSummaryStats } from "@/types/instructor-payments";
import { DollarSign, Users, TrendingUp, AlertCircle } from "lucide-react";

interface PaymentSummaryCardsProps {
  stats: PaymentSummaryStats;
}

export function PaymentSummaryCards({ stats }: PaymentSummaryCardsProps) {
  const cards = [
    {
      title: "Total Unpaid Amount",
      value: formatCurrency(stats.totalUnpaidAmount),
      description: `${stats.totalInstructorsWithUnpaidBalance} instructors with unpaid balances`,
      icon: AlertCircle,
      color: "text-red-600",
      bgColor: "bg-red-50",
    },
    {
      title: "Total Paid All Time",
      value: formatCurrency(stats.totalPaidAllTime),
      description: "Total amount paid to instructors",
      icon: DollarSign,
      color: "text-green-600",
      bgColor: "bg-green-50",
    },
    {
      title: "Total Earnings All Time",
      value: formatCurrency(stats.totalEarningsAllTime),
      description: "Total instructor earnings generated",
      icon: TrendingUp,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      title: "Active Instructors",
      value: stats.totalInstructorsWithEarnings.toString(),
      description: "Instructors with earnings",
      icon: Users,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {cards.map((card, index) => {
        const Icon = card.icon;
        return (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {card.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${card.bgColor}`}>
                <Icon className={`h-4 w-4 ${card.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{card.value}</div>
              <p className="text-xs text-muted-foreground">
                {card.description}
              </p>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
