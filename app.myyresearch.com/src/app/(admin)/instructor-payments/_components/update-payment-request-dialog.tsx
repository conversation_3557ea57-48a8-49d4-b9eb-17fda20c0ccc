"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { formatCurrency, formatDate } from "@/lib/utils";
import { PaymentRequest, PaymentRequestStatus } from "@/types/instructor-payments";
import { useUpdateAdminPaymentRequest } from "@/hooks/use-instructor-payments";
import { updatePaymentRequestSchema, type UpdatePaymentRequestSchema } from "../_lib/validations";

interface UpdatePaymentRequestDialogProps {
  request: PaymentRequest;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function UpdatePaymentRequestDialog({
  request,
  open,
  onOpenChange,
}: UpdatePaymentRequestDialogProps) {
  const [isPending, setIsPending] = React.useState(false);
  
  const updatePaymentRequest = useUpdateAdminPaymentRequest();

  const form = useForm<UpdatePaymentRequestSchema>({
    resolver: zodResolver(updatePaymentRequestSchema),
    defaultValues: {
      status: request.status,
      adminNotes: request.adminNotes || "",
    },
  });

  async function onSubmit(data: UpdatePaymentRequestSchema) {
    setIsPending(true);
    
    try {
      const response = await updatePaymentRequest.mutateAsync({
        requestId: request._id,
        data,
      });
      
      if (response.success) {
        toast.success("Payment request updated successfully");
        onOpenChange(false);
      } else {
        toast.error(response.message || "Failed to update payment request");
      }
    } catch (error) {
      toast.error("An error occurred while updating the payment request");
      console.error("Payment request update error:", error);
    } finally {
      setIsPending(false);
    }
  }

  const getStatusBadge = (status: PaymentRequestStatus) => {
    switch (status) {
      case PaymentRequestStatus.PENDING:
        return <Badge variant="outline" className="text-yellow-600">Pending</Badge>;
      case PaymentRequestStatus.APPROVED:
        return <Badge variant="outline" className="text-green-600">Approved</Badge>;
      case PaymentRequestStatus.REJECTED:
        return <Badge variant="outline" className="text-red-600">Rejected</Badge>;
      case PaymentRequestStatus.PAID:
        return <Badge variant="outline" className="text-blue-600">Paid</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Payment Request Details</DialogTitle>
          <DialogDescription>
            Review and update the payment request status
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Request Details */}
          <div className="rounded-lg border p-4 bg-muted/50">
            <h4 className="font-medium mb-3">Request Information</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Instructor ID:</span>
                <div className="font-mono">{request.instructorId}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Requested Amount:</span>
                <div className="font-mono font-medium">
                  {formatCurrency(request.requestedAmount, request.currency)}
                </div>
              </div>
              <div>
                <span className="text-muted-foreground">Current Status:</span>
                <div className="mt-1">{getStatusBadge(request.status)}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Request Date:</span>
                <div>{formatDate(request.createdAt)}</div>
              </div>
              {request.requestNotes && (
                <div className="col-span-2">
                  <span className="text-muted-foreground">Request Notes:</span>
                  <div className="mt-1 p-2 bg-background rounded border text-sm">
                    {request.requestNotes}
                  </div>
                </div>
              )}
              {request.processedBy && (
                <div className="col-span-2">
                  <span className="text-muted-foreground">Processed By:</span>
                  <div className="font-mono">{request.processedBy}</div>
                </div>
              )}
            </div>
          </div>

          {/* Update Form */}
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Update Status</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value={PaymentRequestStatus.PENDING}>Pending</SelectItem>
                        <SelectItem value={PaymentRequestStatus.APPROVED}>Approved</SelectItem>
                        <SelectItem value={PaymentRequestStatus.REJECTED}>Rejected</SelectItem>
                        <SelectItem value={PaymentRequestStatus.PAID}>Paid</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="adminNotes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Admin Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Add notes about this payment request..."
                        className="resize-none"
                        rows={4}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  disabled={isPending}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isPending}>
                  {isPending ? "Updating..." : "Update Request"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
