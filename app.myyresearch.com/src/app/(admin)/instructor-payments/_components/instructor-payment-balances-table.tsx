"use client";

import * as React from "react";
import { type ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/data-table/data-table";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { DataTableToolbar } from "@/components/data-table/data-table-toolbar";
import { useDataTable } from "@/hooks/data-table/use-data-table";
import { type DataTableFilterField } from "@/types";
import { formatCurrency, formatDate } from "@/lib/utils";
import {
  InstructorPaymentBalance,
  GetInstructorBalancesResponse,
} from "@/types/instructor-payments";
import { Eye, DollarSign } from "lucide-react";
import { ProcessPaymentDialog } from "./process-payment-dialog";
import { ViewInstructorDetailsDialog } from "./view-instructor-details-dialog";

interface InstructorPaymentBalancesTableProps {
  balances: GetInstructorBalancesResponse;
}

export function InstructorPaymentBalancesTable({
  balances,
}: InstructorPaymentBalancesTableProps) {
  const [selectedInstructor, setSelectedInstructor] =
    React.useState<InstructorPaymentBalance | null>(null);
  const [showProcessPayment, setShowProcessPayment] = React.useState(false);
  const [showInstructorDetails, setShowInstructorDetails] =
    React.useState(false);

  const filterFields: DataTableFilterField<InstructorPaymentBalance>[] = [
    {
      id: "instructorName",
      label: "Instructor Name",
      placeholder: "Search instructors...",
    },
  ];

  const columns: ColumnDef<InstructorPaymentBalance>[] = [
    {
      accessorKey: "instructorName",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Instructor" />
      ),
      cell: ({ row }) => {
        const instructor = row.original;
        return (
          <div className="flex flex-col">
            <span className="font-medium">{instructor.instructorName}</span>
            <span className="text-sm text-muted-foreground">
              {instructor.instructorEmail}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "currentBalance",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Current Balance" />
      ),
      cell: ({ row }) => {
        const balance = row.getValue("currentBalance") as number;
        return (
          <div className="flex items-center">
            <Badge
              variant={balance > 0 ? "destructive" : "secondary"}
              className="font-mono"
            >
              {formatCurrency(balance)}
            </Badge>
          </div>
        );
      },
    },
    {
      accessorKey: "totalEarnings",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Total Earnings" />
      ),
      cell: ({ row }) => {
        const earnings = row.getValue("totalEarnings") as number;
        return (
          <span className="font-mono text-sm">{formatCurrency(earnings)}</span>
        );
      },
    },
    {
      accessorKey: "totalPaid",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Total Paid" />
      ),
      cell: ({ row }) => {
        const paid = row.getValue("totalPaid") as number;
        return (
          <span className="font-mono text-sm text-green-600">
            {formatCurrency(paid)}
          </span>
        );
      },
    },
    {
      accessorKey: "unpaidOrderItemsCount",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Unpaid Items" />
      ),
      cell: ({ row }) => {
        const count = row.getValue("unpaidOrderItemsCount") as number;
        return <Badge variant="outline">{count} items</Badge>;
      },
    },
    {
      accessorKey: "lastPaymentDate",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Last Payment" />
      ),
      cell: ({ row }) => {
        const date = row.getValue("lastPaymentDate") as Date | undefined;
        return (
          <span className="text-sm text-muted-foreground">
            {date ? formatDate(date) : "Never"}
          </span>
        );
      },
    },
    {
      accessorKey: "firstEarningDate",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="First Earning" />
      ),
      cell: ({ row }) => {
        const date = row.getValue("firstEarningDate") as Date;
        return (
          <span className="text-sm text-muted-foreground">
            {formatDate(date)}
          </span>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const instructor = row.original;
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setSelectedInstructor(instructor);
                setShowInstructorDetails(true);
              }}
            >
              <Eye className="h-4 w-4" />
              View Details
            </Button>
            {instructor.currentBalance > 0 && (
              <Button
                variant="default"
                size="sm"
                onClick={() => {
                  setSelectedInstructor(instructor);
                  setShowProcessPayment(true);
                }}
              >
                <DollarSign className="h-4 w-4" />
                Process Payment
              </Button>
            )}
          </div>
        );
      },
    },
  ];

  const { table } = useDataTable({
    data: balances.instructors,
    columns,
    pageCount: -1,
    filterFields,
    enableAdvancedFilter: false,
    initialState: {
      sorting: [{ id: "currentBalance", desc: true }],
      columnPinning: { right: ["actions"] },
    },
    getRowId: (originalRow) => originalRow.instructorId,
    shallow: false,
    clearOnDefault: true,
  });

  return (
    <>
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">
              Instructor Payment Balances
            </h2>
            <p className="text-muted-foreground">
              Manage instructor payments and view balance details
            </p>
          </div>
          <div className="text-sm text-muted-foreground">
            Total: {formatCurrency(balances.totalUnpaidAmount)} unpaid across{" "}
            {balances.totalInstructors} instructors
          </div>
        </div>

        <DataTable table={table}>
          <DataTableToolbar table={table} filterFields={filterFields} />
        </DataTable>
      </div>

      {/* Process Payment Dialog */}
      {selectedInstructor && (
        <ProcessPaymentDialog
          instructor={selectedInstructor}
          open={showProcessPayment}
          onOpenChange={setShowProcessPayment}
        />
      )}

      {/* View Instructor Details Dialog */}
      {selectedInstructor && (
        <ViewInstructorDetailsDialog
          instructor={selectedInstructor}
          open={showInstructorDetails}
          onOpenChange={setShowInstructorDetails}
        />
      )}
    </>
  );
}
