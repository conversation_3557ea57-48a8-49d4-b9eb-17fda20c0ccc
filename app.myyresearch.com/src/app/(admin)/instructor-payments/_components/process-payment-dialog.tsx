"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { formatCurrency } from "@/lib/utils";
import { InstructorPaymentBalance, PaymentMethod } from "@/types/instructor-payments";
import { useCreateAdminPaymentRecord, useAdminInstructorBalanceById } from "@/hooks/use-instructor-payments";
import { createPaymentRecordSchema, type CreatePaymentRecordSchema } from "../_lib/validations";

interface ProcessPaymentDialogProps {
  instructor: InstructorPaymentBalance;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function ProcessPaymentDialog({
  instructor,
  open,
  onOpenChange,
}: ProcessPaymentDialogProps) {
  const [isPending, setIsPending] = React.useState(false);
  
  const { data: instructorDetails } = useAdminInstructorBalanceById(instructor.instructorId);
  const createPaymentRecord = useCreateAdminPaymentRecord();

  const form = useForm<CreatePaymentRecordSchema>({
    resolver: zodResolver(createPaymentRecordSchema),
    defaultValues: {
      instructorId: instructor.instructorId,
      amount: instructor.currentBalance,
      currency: "usd",
      paymentMethod: PaymentMethod.PAYPAL,
      paymentDate: new Date().toISOString().split('T')[0],
      transactionId: "",
      notes: "",
      orderItemDetailsIds: [],
    },
  });

  // Update order item details IDs when instructor details are loaded
  React.useEffect(() => {
    if (instructorDetails?.unpaidOrderItems) {
      form.setValue(
        "orderItemDetailsIds",
        instructorDetails.unpaidOrderItems.map(item => item._id)
      );
    }
  }, [instructorDetails, form]);

  async function onSubmit(data: CreatePaymentRecordSchema) {
    setIsPending(true);
    
    try {
      const response = await createPaymentRecord.mutateAsync(data);
      
      if (response.success) {
        toast.success("Payment record created successfully");
        onOpenChange(false);
        form.reset();
      } else {
        toast.error(response.message || "Failed to create payment record");
      }
    } catch (error) {
      toast.error("An error occurred while processing the payment");
      console.error("Payment processing error:", error);
    } finally {
      setIsPending(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Process Payment</DialogTitle>
          <DialogDescription>
            Process payment for {instructor.instructorName} - Current balance: {formatCurrency(instructor.currentBalance)}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Payment Amount</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0.01"
                        max={instructor.currentBalance}
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="paymentMethod"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Payment Method</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select payment method" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value={PaymentMethod.PAYPAL}>PayPal</SelectItem>
                        <SelectItem value={PaymentMethod.BANK_TRANSFER}>Bank Transfer</SelectItem>
                        <SelectItem value={PaymentMethod.STRIPE}>Stripe</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="paymentDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Payment Date</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="transactionId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Transaction ID (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter transaction ID" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Add any notes about this payment..."
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {instructorDetails && (
              <div className="rounded-lg border p-4 bg-muted/50">
                <h4 className="font-medium mb-2">Payment Summary</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Unpaid Order Items:</span>
                    <span>{instructorDetails.unpaidOrderItems.length} items</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total Instructor Earnings:</span>
                    <span className="font-mono">{formatCurrency(instructor.currentBalance)}</span>
                  </div>
                  <div className="flex justify-between font-medium">
                    <span>Payment Amount:</span>
                    <span className="font-mono">{formatCurrency(form.watch("amount"))}</span>
                  </div>
                </div>
              </div>
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isPending}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isPending}>
                {isPending ? "Processing..." : "Process Payment"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
