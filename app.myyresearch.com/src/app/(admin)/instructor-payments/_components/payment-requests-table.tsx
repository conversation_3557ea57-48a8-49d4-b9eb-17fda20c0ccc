"use client";

import * as React from "react";
import { type ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { DataTable } from "@/components/data-table/data-table";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { DataTableToolbar } from "@/components/data-table/data-table-toolbar";
import { useDataTable } from "@/hooks/data-table/use-data-table";
import { type DataTableFilterField } from "@/types";
import { formatCurrency, formatDate } from "@/lib/utils";
import {
  PaymentRequest,
  PaymentRequestStatus,
} from "@/types/instructor-payments";
import { CheckCircle, XCircle, Clock, DollarSign, Eye } from "lucide-react";
import { UpdatePaymentRequestDialog } from "./update-payment-request-dialog";

interface PaymentRequestsTableProps {
  requests: PaymentRequest[];
}

export function PaymentRequestsTable({ requests }: PaymentRequestsTableProps) {
  const [selectedRequest, setSelectedRequest] =
    React.useState<PaymentRequest | null>(null);
  const [showUpdateDialog, setShowUpdateDialog] = React.useState(false);

  const filterFields: DataTableFilterField<PaymentRequest>[] = [
    {
      id: "instructorId",
      label: "Instructor ID",
      placeholder: "Search by instructor ID...",
    },
    {
      id: "status",
      label: "Status",
      options: Object.values(PaymentRequestStatus).map((status) => ({
        label: status.charAt(0).toUpperCase() + status.slice(1),
        value: status,
      })),
    },
  ];

  const getStatusBadge = (status: PaymentRequestStatus) => {
    switch (status) {
      case PaymentRequestStatus.PENDING:
        return (
          <Badge variant="outline" className="text-yellow-600">
            <Clock className="h-3 w-3 mr-1" />
            Pending
          </Badge>
        );
      case PaymentRequestStatus.APPROVED:
        return (
          <Badge variant="outline" className="text-green-600">
            <CheckCircle className="h-3 w-3 mr-1" />
            Approved
          </Badge>
        );
      case PaymentRequestStatus.REJECTED:
        return (
          <Badge variant="outline" className="text-red-600">
            <XCircle className="h-3 w-3 mr-1" />
            Rejected
          </Badge>
        );
      case PaymentRequestStatus.PAID:
        return (
          <Badge variant="outline" className="text-blue-600">
            <DollarSign className="h-3 w-3 mr-1" />
            Paid
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const columns: ColumnDef<PaymentRequest>[] = [
    {
      accessorKey: "instructorId",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Instructor" />
      ),
      cell: ({ row }) => {
        const request = row.original;
        return (
          <div className="flex flex-col">
            <span className="font-medium">
              Instructor ID: {request.instructorId}
            </span>
            <span className="text-sm text-muted-foreground">
              Request ID: {request._id.slice(-8)}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "requestedAmount",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Requested Amount" />
      ),
      cell: ({ row }) => {
        const amount = row.getValue("requestedAmount") as number;
        const currency = row.original.currency;
        return (
          <span className="font-mono font-medium">
            {formatCurrency(amount, currency)}
          </span>
        );
      },
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Status" />
      ),
      cell: ({ row }) => {
        const status = row.getValue("status") as PaymentRequestStatus;
        return getStatusBadge(status);
      },
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Request Date" />
      ),
      cell: ({ row }) => {
        const date = row.getValue("createdAt") as Date;
        return (
          <span className="text-sm text-muted-foreground">
            {formatDate(date)}
          </span>
        );
      },
    },
    {
      accessorKey: "approvedAt",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Processed Date" />
      ),
      cell: ({ row }) => {
        const request = row.original;
        const processedDate =
          request.approvedAt || request.rejectedAt || request.paidAt;
        return (
          <span className="text-sm text-muted-foreground">
            {processedDate ? formatDate(processedDate) : "Not processed"}
          </span>
        );
      },
    },
    {
      accessorKey: "requestNotes",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Request Notes" />
      ),
      cell: ({ row }) => {
        const notes = row.getValue("requestNotes") as string;
        return (
          <div className="max-w-[200px] truncate">{notes || "No notes"}</div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const request = row.original;
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setSelectedRequest(request);
                setShowUpdateDialog(true);
              }}
            >
              <Eye className="h-4 w-4" />
              {request.status === PaymentRequestStatus.PENDING
                ? "Review"
                : "View"}
            </Button>
          </div>
        );
      },
    },
  ];

  const { table } = useDataTable({
    data: requests,
    columns,
    pageCount: -1,
    filterFields,
    enableAdvancedFilter: false,
    initialState: {
      sorting: [{ id: "createdAt", desc: true }],
      columnPinning: { right: ["actions"] },
    },
    getRowId: (originalRow) => originalRow._id,
    shallow: false,
    clearOnDefault: true,
  });

  return (
    <>
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">
              Payment Requests
            </h2>
            <p className="text-muted-foreground">
              Review and manage instructor payment requests
            </p>
          </div>
          <div className="text-sm text-muted-foreground">
            Total: {requests.length} requests
          </div>
        </div>

        <DataTable table={table}>
          <DataTableToolbar table={table} filterFields={filterFields} />
        </DataTable>
      </div>

      {/* Update Payment Request Dialog */}
      {selectedRequest && (
        <UpdatePaymentRequestDialog
          request={selectedRequest}
          open={showUpdateDialog}
          onOpenChange={setShowUpdateDialog}
        />
      )}
    </>
  );
}
