"use client";

import * as React from "react";
import { type ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { DataTable } from "@/components/data-table/data-table";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { DataTableToolbar } from "@/components/data-table/data-table-toolbar";
import { useDataTable } from "@/hooks/data-table/use-data-table";
import { type DataTableFilterField } from "@/types";
import { formatCurrency, formatDate } from "@/lib/utils";
import { PaymentRecord, PaymentMethod } from "@/types/instructor-payments";
import { Eye, CreditCard, Building, Zap } from "lucide-react";
import { ViewPaymentRecordDialog } from "./view-payment-record-dialog";

interface PaymentHistoryTableProps {
  payments: PaymentRecord[];
}

export function PaymentHistoryTable({ payments }: PaymentHistoryTableProps) {
  const [selectedPayment, setSelectedPayment] =
    React.useState<PaymentRecord | null>(null);
  const [showViewDialog, setShowViewDialog] = React.useState(false);

  const filterFields: DataTableFilterField<PaymentRecord>[] = [
    {
      id: "instructorId",
      label: "Instructor ID",
      placeholder: "Search by instructor ID...",
    },
    {
      id: "transactionId",
      label: "Transaction ID",
      placeholder: "Search by transaction ID...",
    },
    {
      id: "paymentMethod",
      label: "Payment Method",
      options: Object.values(PaymentMethod).map((method) => ({
        label: method.replace("_", " ").toUpperCase(),
        value: method,
      })),
    },
  ];

  const getPaymentMethodIcon = (method: PaymentMethod) => {
    switch (method) {
      case PaymentMethod.PAYPAL:
        return <CreditCard className="h-4 w-4" />;
      case PaymentMethod.BANK_TRANSFER:
        return <Building className="h-4 w-4" />;
      case PaymentMethod.STRIPE:
        return <Zap className="h-4 w-4" />;
      default:
        return <CreditCard className="h-4 w-4" />;
    }
  };

  const getPaymentMethodBadge = (method: PaymentMethod) => {
    const icon = getPaymentMethodIcon(method);
    const label = method.replace("_", " ").toUpperCase();

    return (
      <Badge variant="outline" className="flex items-center gap-1">
        {icon}
        {label}
      </Badge>
    );
  };

  const columns: ColumnDef<PaymentRecord>[] = [
    {
      accessorKey: "instructorId",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Instructor" />
      ),
      cell: ({ row }) => {
        const payment = row.original;
        return (
          <div className="flex flex-col">
            <span className="font-medium">ID: {payment.instructorId}</span>
            <span className="text-sm text-muted-foreground">
              Payment ID: {payment._id.slice(-8)}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "amount",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Amount" />
      ),
      cell: ({ row }) => {
        const amount = row.getValue("amount") as number;
        const currency = row.original.currency;
        return (
          <span className="font-mono font-medium text-green-600">
            {formatCurrency(amount, currency)}
          </span>
        );
      },
    },
    {
      accessorKey: "paymentMethod",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Payment Method" />
      ),
      cell: ({ row }) => {
        const method = row.getValue("paymentMethod") as PaymentMethod;
        return getPaymentMethodBadge(method);
      },
    },
    {
      accessorKey: "paymentDate",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Payment Date" />
      ),
      cell: ({ row }) => {
        const date = row.getValue("paymentDate") as Date;
        return <span className="text-sm">{formatDate(date)}</span>;
      },
    },
    {
      accessorKey: "transactionId",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Transaction ID" />
      ),
      cell: ({ row }) => {
        const transactionId = row.getValue("transactionId") as string;
        return (
          <div className="max-w-[150px]">
            {transactionId ? (
              <span className="font-mono text-sm truncate block">
                {transactionId}
              </span>
            ) : (
              <span className="text-muted-foreground text-sm">No ID</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "orderItemDetailsIds",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Order Items" />
      ),
      cell: ({ row }) => {
        const orderItems = row.getValue("orderItemDetailsIds") as string[];
        return <Badge variant="secondary">{orderItems.length} items</Badge>;
      },
    },
    {
      accessorKey: "processedBy",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Processed By" />
      ),
      cell: ({ row }) => {
        const processedBy = row.getValue("processedBy") as string;
        return (
          <span className="text-sm text-muted-foreground font-mono">
            {processedBy.slice(-8)}
          </span>
        );
      },
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Created" />
      ),
      cell: ({ row }) => {
        const date = row.getValue("createdAt") as Date;
        return (
          <span className="text-sm text-muted-foreground">
            {formatDate(date)}
          </span>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const payment = row.original;
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setSelectedPayment(payment);
                setShowViewDialog(true);
              }}
            >
              <Eye className="h-4 w-4" />
              View Details
            </Button>
          </div>
        );
      },
    },
  ];

  const { table } = useDataTable({
    data: payments,
    columns,
    pageCount: -1,
    filterFields,
    enableAdvancedFilter: false,
    initialState: {
      sorting: [{ id: "paymentDate", desc: true }],
      columnPinning: { right: ["actions"] },
    },
    getRowId: (originalRow) => originalRow._id,
    shallow: false,
    clearOnDefault: true,
  });

  return (
    <>
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">
              Payment History
            </h2>
            <p className="text-muted-foreground">
              View all payment records made to instructors
            </p>
          </div>
          <div className="text-sm text-muted-foreground">
            Total: {payments.length} payments
          </div>
        </div>

        <DataTable table={table}>
          <DataTableToolbar table={table} filterFields={filterFields} />
        </DataTable>
      </div>

      {/* View Payment Record Dialog */}
      {selectedPayment && (
        <ViewPaymentRecordDialog
          payment={selectedPayment}
          open={showViewDialog}
          onOpenChange={setShowViewDialog}
        />
      )}
    </>
  );
}
