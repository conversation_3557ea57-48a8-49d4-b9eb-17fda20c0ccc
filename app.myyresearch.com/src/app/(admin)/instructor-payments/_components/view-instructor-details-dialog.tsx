"use client";

import * as React from "react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { formatCurrency, formatDate } from "@/lib/utils";
import { InstructorPaymentBalance } from "@/types/instructor-payments";
import { useAdminInstructorBalanceById } from "@/hooks/use-instructor-payments";
import { ExternalLink, Calendar, DollarSign, Package } from "lucide-react";

interface ViewInstructorDetailsDialogProps {
  instructor: InstructorPaymentBalance;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function ViewInstructorDetailsDialog({
  instructor,
  open,
  onOpenChange,
}: ViewInstructorDetailsDialogProps) {
  const { data: instructorDetails, isLoading } = useAdminInstructorBalanceById(instructor.instructorId);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Instructor Payment Details</DialogTitle>
          <DialogDescription>
            Detailed payment information for {instructor.instructorName}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Instructor Summary */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium">Instructor Information</h4>
              <div className="space-y-1 text-sm">
                <div><strong>Name:</strong> {instructor.instructorName}</div>
                <div><strong>Email:</strong> {instructor.instructorEmail}</div>
                <div><strong>First Earning:</strong> {formatDate(instructor.firstEarningDate)}</div>
                <div><strong>Last Payment:</strong> {instructor.lastPaymentDate ? formatDate(instructor.lastPaymentDate) : "Never"}</div>
              </div>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">Payment Summary</h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Total Earnings:</span>
                  <span className="font-mono">{formatCurrency(instructor.totalEarnings)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Total Paid:</span>
                  <span className="font-mono text-green-600">{formatCurrency(instructor.totalPaid)}</span>
                </div>
                <div className="flex justify-between font-medium">
                  <span>Current Balance:</span>
                  <Badge variant={instructor.currentBalance > 0 ? "destructive" : "secondary"}>
                    {formatCurrency(instructor.currentBalance)}
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Unpaid Order Items */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Package className="h-4 w-4" />
              <h4 className="font-medium">Unpaid Order Items ({instructor.unpaidOrderItemsCount})</h4>
            </div>
            
            {isLoading ? (
              <div className="space-y-2">
                {Array.from({ length: 3 }).map((_, i) => (
                  <Skeleton key={i} className="h-16 w-full" />
                ))}
              </div>
            ) : instructorDetails?.unpaidOrderItems.length ? (
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {instructorDetails.unpaidOrderItems.map((item) => (
                  <div key={item._id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          {item.itemType}
                        </Badge>
                        <span className="font-medium">{item.itemTitle}</span>
                      </div>
                      <div className="text-sm text-muted-foreground mt-1">
                        Order Date: {formatDate(item.orderDate)} • ID: {item.publicId}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-mono text-sm">{formatCurrency(item.totalPrice)}</div>
                      <div className="font-mono text-xs text-green-600">
                        Earning: {formatCurrency(item.instructorEarning)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                No unpaid order items
              </div>
            )}
          </div>

          <Separator />

          {/* Payment History */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              <h4 className="font-medium">Payment History</h4>
            </div>
            
            {isLoading ? (
              <div className="space-y-2">
                {Array.from({ length: 2 }).map((_, i) => (
                  <Skeleton key={i} className="h-16 w-full" />
                ))}
              </div>
            ) : instructorDetails?.paymentHistory.length ? (
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {instructorDetails.paymentHistory.map((payment) => (
                  <div key={payment._id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span className="font-medium">{formatDate(payment.paymentDate)}</span>
                        <Badge variant="outline" className="text-xs">
                          {payment.paymentMethod}
                        </Badge>
                      </div>
                      {payment.notes && (
                        <div className="text-sm text-muted-foreground mt-1">
                          {payment.notes}
                        </div>
                      )}
                      <div className="text-xs text-muted-foreground mt-1">
                        {payment.orderItemsCount} items • {payment.transactionId && `TXN: ${payment.transactionId}`}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-mono font-medium text-green-600">
                        {formatCurrency(payment.amount)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                No payment history
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
