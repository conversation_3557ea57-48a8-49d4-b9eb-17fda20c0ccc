"use client";

import * as React from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { formatCurrency, formatDate } from "@/lib/utils";
import { PaymentRecord, PaymentMethod } from "@/types/instructor-payments";
import { CreditCard, Building, Zap, Copy, ExternalLink } from "lucide-react";
import { toast } from "sonner";

interface ViewPaymentRecordDialogProps {
  payment: PaymentRecord;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function ViewPaymentRecordDialog({
  payment,
  open,
  onOpenChange,
}: ViewPaymentRecordDialogProps) {
  const getPaymentMethodIcon = (method: PaymentMethod) => {
    switch (method) {
      case PaymentMethod.PAYPAL:
        return <CreditCard className="h-4 w-4" />;
      case PaymentMethod.BANK_TRANSFER:
        return <Building className="h-4 w-4" />;
      case PaymentMethod.STRIPE:
        return <Zap className="h-4 w-4" />;
      default:
        return <CreditCard className="h-4 w-4" />;
    }
  };

  const getPaymentMethodBadge = (method: PaymentMethod) => {
    const icon = getPaymentMethodIcon(method);
    const label = method.replace('_', ' ').toUpperCase();
    
    return (
      <Badge variant="outline" className="flex items-center gap-1">
        {icon}
        {label}
      </Badge>
    );
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${label} copied to clipboard`);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Payment Record Details</DialogTitle>
          <DialogDescription>
            Complete details of the payment record
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Payment Summary */}
          <div className="rounded-lg border p-4 bg-muted/50">
            <h4 className="font-medium mb-3">Payment Summary</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="text-sm text-muted-foreground">Amount Paid</span>
                <div className="text-2xl font-bold text-green-600">
                  {formatCurrency(payment.amount, payment.currency)}
                </div>
              </div>
              <div>
                <span className="text-sm text-muted-foreground">Payment Method</span>
                <div className="mt-1">
                  {getPaymentMethodBadge(payment.paymentMethod)}
                </div>
              </div>
            </div>
          </div>

          {/* Payment Details */}
          <div className="rounded-lg border p-4">
            <h4 className="font-medium mb-3">Payment Information</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Payment ID:</span>
                <div className="flex items-center gap-2 mt-1">
                  <span className="font-mono">{payment._id}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(payment._id, "Payment ID")}
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
              </div>
              <div>
                <span className="text-muted-foreground">Instructor ID:</span>
                <div className="flex items-center gap-2 mt-1">
                  <span className="font-mono">{payment.instructorId}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(payment.instructorId, "Instructor ID")}
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
              </div>
              <div>
                <span className="text-muted-foreground">Payment Date:</span>
                <div className="mt-1">{formatDate(payment.paymentDate)}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Processed By:</span>
                <div className="font-mono mt-1">{payment.processedBy}</div>
              </div>
              {payment.transactionId && (
                <div className="col-span-2">
                  <span className="text-muted-foreground">Transaction ID:</span>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="font-mono">{payment.transactionId}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(payment.transactionId!, "Transaction ID")}
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Order Items */}
          <div className="rounded-lg border p-4">
            <h4 className="font-medium mb-3">Order Items Paid</h4>
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Number of Items:</span>
                <Badge variant="secondary">
                  {payment.orderItemDetailsIds.length} items
                </Badge>
              </div>
              <div className="text-xs text-muted-foreground">
                <span>Order Item IDs:</span>
                <div className="mt-1 p-2 bg-muted rounded font-mono text-xs max-h-20 overflow-y-auto">
                  {payment.orderItemDetailsIds.map((id, index) => (
                    <div key={id} className="flex items-center justify-between">
                      <span>{index + 1}. {id}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(id, `Order Item ${index + 1} ID`)}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Notes */}
          {payment.notes && (
            <div className="rounded-lg border p-4">
              <h4 className="font-medium mb-3">Notes</h4>
              <div className="text-sm bg-muted p-3 rounded">
                {payment.notes}
              </div>
            </div>
          )}

          {/* Timestamps */}
          <div className="rounded-lg border p-4">
            <h4 className="font-medium mb-3">Record Information</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Created:</span>
                <div className="mt-1">{formatDate(payment.createdAt)}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Last Updated:</span>
                <div className="mt-1">{formatDate(payment.updatedAt)}</div>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
