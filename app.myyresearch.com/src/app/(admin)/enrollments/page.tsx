import * as React from "react";
import Link from "next/link";
import { type SearchParams } from "@/types";

import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Skeleton } from "@/components/ui/skeleton";
import { DataTableSkeleton } from "@/components/data-table/data-table-skeleton";
import { DateRangePicker } from "@/components/date-range-picker";
import { Shell } from "@/components/shell";

import { OrderItemsTable } from "@/components/order-items/order-items-table";
import { OrderItemType } from "@/types/orders";
import {
  getPurchasedCourses,
  getPurchasedCourseCategoryCounts,
} from "./_lib/queries";
import { searchParamsCache } from "./_lib/validations";
import { type Metadata } from "next";

export const metadata: Metadata = {
  title: "Enrollments",
  description: "Manage course enrollments",
};

interface EnrollmentsPageProps {
  searchParams: Promise<SearchParams>;
}

export default async function EnrollmentsPage({
  searchParams,
}: EnrollmentsPageProps) {
  const search = searchParamsCache.parse(await searchParams);

  const promises = Promise.all([
    getPurchasedCourses(search),
    getPurchasedCourseCategoryCounts(),
  ]);

  const [coursesResponse] = await promises;

  return (
    <ContentLayout title="Enrollments">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">Home</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Enrollments</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <Shell className="gap-2">
        <React.Suspense fallback={<Skeleton className="h-7 w-52" />}>
          <DateRangePicker
            triggerSize="sm"
            triggerClassName="ml-auto w-56 sm:w-60"
            align="end"
          />
        </React.Suspense>
        <React.Suspense
          fallback={
            <DataTableSkeleton
              columnCount={8}
              searchableColumnCount={2}
              filterableColumnCount={2}
              cellWidths={[
                "10rem",
                "15rem",
                "10rem",
                "8rem",
                "10rem",
                "12rem",
                "12rem",
                "8rem",
              ]}
              shrinkZero
            />
          }
        >
          <OrderItemsTable
            data={coursesResponse.data?.courses ?? []}
            pageCount={coursesResponse.pageCount ?? 0}
            itemType={OrderItemType.COURSE}
            title="Course Enrollments"
          />
        </React.Suspense>
      </Shell>
    </ContentLayout>
  );
}
