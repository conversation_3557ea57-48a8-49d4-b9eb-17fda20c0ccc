"use client";

import { type Table } from "@tanstack/react-table";
import { Download } from "lucide-react";

import { type MinimalEnrollment } from "@/types/enrollment";
import { exportTableToCSV } from "@/lib/data-table/export";
import { Button } from "@/components/ui/button";

interface EnrollmentsTableToolbarActionsProps {
  table: Table<MinimalEnrollment>;
  onRefresh?: () => void;
}

export function EnrollmentsTableToolbarActions({
  table,
  onRefresh,
}: EnrollmentsTableToolbarActionsProps) {
  return (
    <div className="flex items-center gap-2">
      <Button
        variant="outline"
        size="sm"
        onClick={() =>
          exportTableToCSV(table, {
            filename: "enrollments",
            excludeColumns: ["select", "actions"],
          })
        }
        className="gap-2"
      >
        <Download className="size-4" aria-hidden="true" />
        Export
      </Button>
    </div>
  );
}
