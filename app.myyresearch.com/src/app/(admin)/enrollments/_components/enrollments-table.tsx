"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import type {
  DataTableAdvancedFilterField,
  DataTableFilterField,
  DataTableRowAction,
} from "@/types";

import { EnrollmentStatus, type MinimalEnrollment } from "@/types/enrollment";
import { toSentenceCase } from "@/lib/data-table/utils";
import { useDataTable } from "@/hooks/data-table/use-data-table";
import { DataTable } from "@/components/data-table/data-table";
import { DataTableAdvancedToolbar } from "@/components/data-table/data-table-advanced-toolbar";
import { DataTableToolbar } from "@/components/data-table/data-table-toolbar";

import { getColumns } from "./enrollments-table-columns";
import { EnrollmentsTableToolbarActions } from "./enrollments-table-toolbar-actions";

interface EnrollmentsTableProps {
  data: MinimalEnrollment[];
  pageCount: number;
  statusCounts: Record<EnrollmentStatus, number>;
}

export function EnrollmentsTable({
  data,
  pageCount,
  statusCounts,
}: EnrollmentsTableProps) {
  const router = useRouter();

  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<MinimalEnrollment> | null>(null);

  const columns = React.useMemo(
    () => getColumns({ setRowAction }),
    [setRowAction]
  );

  const filterFields: DataTableFilterField<MinimalEnrollment>[] = [
    {
      id: "courseTitle",
      label: "Course",
      placeholder: "Filter courses...",
    },
    {
      id: "user",
      label: "User",
      placeholder: "Filter users...",
    },
    {
      id: "status",
      label: "Status",
      options: Object.values(EnrollmentStatus).map((status) => ({
        label: toSentenceCase(status),
        value: status,
        count: statusCounts[status] || 0,
      })),
    },
  ];

  const advancedFilterFields: DataTableAdvancedFilterField<MinimalEnrollment>[] =
    [
      {
        id: "courseTitle",
        label: "Course",
        type: "text",
      },
      {
        id: "user",
        label: "User",
        type: "text",
      },
      {
        id: "status",
        label: "Status",
        type: "multi-select",
        options: Object.values(EnrollmentStatus).map((status) => ({
          label: toSentenceCase(status),
          value: status,
          count: statusCounts[status] || 0,
        })),
      },
      {
        id: "createdAt",
        label: "Enrolled at",
        type: "date",
      },
      {
        id: "lastAccessedAt",
        label: "Last accessed",
        type: "date",
      },
    ];

  const { table } = useDataTable({
    data,
    columns,
    pageCount,
    filterFields,
    enableAdvancedFilter: true,
    initialState: {
      sorting: [{ id: "createdAt", desc: true }],
      columnPinning: { right: ["actions"] },
    },
    getRowId: (originalRow) => originalRow._id,
  });

  return (
    <DataTable table={table}>
      <DataTableAdvancedToolbar
        table={table}
        filterFields={advancedFilterFields}
      >
        <EnrollmentsTableToolbarActions
          table={table}
          onRefresh={() => router.refresh()}
        />
      </DataTableAdvancedToolbar>
    </DataTable>
  );
}
