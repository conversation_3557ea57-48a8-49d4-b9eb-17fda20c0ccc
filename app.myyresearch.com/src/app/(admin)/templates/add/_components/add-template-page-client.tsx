"use client";

import { useState, useRef } from "react";
import { Shell } from "@/components/shell";
import { ContentLayout } from "@/components/admin-panel/content-layout";
import { TemplateDetailForm } from "./template-detail-form";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Card, CardContent } from "@/components/ui/card";
import { SaveIcon, SendIcon, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import { Template, TemplateStatus, TemplateFormValues } from "@/types/template";
import { DocumentStatus, ApiStatus } from "@/types/common";
import { createTemplate } from "@/lib/templates";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

export default function AddTemplatePageClient() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const router = useRouter();
  const formRef = useRef<HTMLFormElement>(null);
  let isDraft = false;

  const handleSubmit = async (data: TemplateFormValues) => {
    setIsSubmitting(true);
    try {
      const templateData = {
        title: data.title,
        slug: data.slug,
        shortDescription: data.shortDescription,
        description: data.description || undefined,
        thumbnail: {
          key: data.thumbnail.key,
          name: data.thumbnail.name,
          size: data.thumbnail.size,
          type: data.thumbnail.type,
        },
        mediaType: data.mediaType,
        category: data.category,
        subcategory: data.subcategory,
        price: data.price,
        discountPrice: data.discountPrice || undefined,
        downloadMedia: {
          key: data.downloadMedia.key,
          name: data.downloadMedia.name,
          size: data.downloadMedia.size,
          type: data.downloadMedia.type,
        },
        previewMedia: data.previewMedia
          ? {
              key: data.previewMedia.key,
              name: data.previewMedia.name,
              size: data.previewMedia.size,
              type: data.previewMedia.type,
            }
          : undefined,
        helpMedia: data.helpMedia
          ? {
              key: data.helpMedia.key,
              name: data.helpMedia.name,
              size: data.helpMedia.size,
              type: data.helpMedia.type,
            }
          : undefined,
        free: data.free,
        version: data.version,
        status:
          isDraft === true ? TemplateStatus.DRAFT : TemplateStatus.PUBLISHED,
      };

      const response = await createTemplate(templateData);

      if (response.status === ApiStatus.FAIL) {
        throw new Error(response.message || "Failed to create template");
      }

      toast({
        title: "Success",
        description: isDraft
          ? "Template saved as draft"
          : "Template published successfully",
      });

      router.push("/templates");
    } catch (error) {
      console.error(error);
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Something went wrong. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const onSaveAsDraft = async (data: TemplateFormValues) => {
    await handleSubmit(data);
  };

  const onPublish = async (data: TemplateFormValues) => {
    await handleSubmit(data);
  };

  const handleButtonClick = (draft: boolean) => {
    isDraft = draft;
    if (formRef.current) {
      formRef.current.requestSubmit();
    }
  };

  return (
    <ContentLayout title="Add New Template">
      <Shell>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-semibold tracking-tight">
                Create New Template
              </h1>
              <p className="text-sm text-muted-foreground">
                Fill in the template details and upload necessary files
              </p>
            </div>
            <div className="flex items-center gap-4">
              <Button
                type="button"
                variant="outline"
                size="lg"
                className="gap-2"
                onClick={() => handleButtonClick(true)}
                disabled={isSubmitting}
              >
                {isSubmitting && isDraft ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Saving...</span>
                  </>
                ) : (
                  <>
                    <SaveIcon className="h-4 w-4" />
                    <span>Save as Draft</span>
                  </>
                )}
              </Button>
              <Button
                type="button"
                size="lg"
                className="gap-2"
                disabled={isSubmitting}
                onClick={() => handleButtonClick(false)}
              >
                {isSubmitting && !isDraft ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Publishing...</span>
                  </>
                ) : (
                  <>
                    <SendIcon className="h-4 w-4" />
                    <span>Publish Template</span>
                  </>
                )}
              </Button>
            </div>
          </div>

          <Separator />

          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/templates">Templates</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href="/templates/add">
                  Add New Template
                </BreadcrumbLink>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>

          <TemplateDetailForm
            ref={formRef}
            onSubmit={onPublish}
            onSaveAsDraft={onSaveAsDraft}
          />
        </div>
      </Shell>
    </ContentLayout>
  );
}
