"use client";

import { useState, useEffect, useRef, forwardRef } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { templateCategories } from "@/data"; // You'll need to create this
import { useToast } from "@/hooks/use-toast";
import {
  CheckCircle,
  XCircle,
  Loader2,
  Pencil,
  FileIcon,
  Download,
  HelpCircle,
} from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { useRouter } from "next/navigation";
import TiptapEditor from "@/components/editor/editor";
import { DocumentStatus, UploadedFile } from "@/types/common";
import axios from "axios";
import Image from "next/image";
import { ThumbnailImageUploader } from "@/components/uploader/thumbnail-image-uploader";
import { FileUploader } from "@/components/uploader/file-uploader";
import {
  Template,
  TemplateStatus,
  TemplateMediaType,
  TemplateTag,
  TemplateFormValues,
} from "@/types/template";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  ImageIcon,
  FileTextIcon,
  TagIcon,
  FolderIcon,
  DollarSignIcon,
  BookOpenIcon,
} from "lucide-react";

const createTemplateSchema = (template?: Template) =>
  z
    .object({
      title: z.string().min(3, "Template title is required"),
      shortDescription: z.string().min(10, "Short description is required"),
      description: z.string().nullable(),
      thumbnail: z.custom<UploadedFile>().refine((file) => {
        if (!template) {
          return file !== null && file !== undefined;
        }
        return true;
      }, "Thumbnail is required for new templates"),
      mediaType: z.nativeEnum(TemplateMediaType),
      category: z.string().min(1, "Category is required"),
      subcategory: z.string().min(1, "Subcategory is required"),
      price: z.number().min(0, "Price must be 0 or greater"),
      discountPrice: z.number().nullable().optional(),
      version: z.string().min(1, "Version is required"),
      downloadMedia: z.custom<UploadedFile>().refine((file) => {
        if (!template) {
          return file !== null && file !== undefined;
        }
        return true;
      }, "Download media is required"),
      previewMedia: z.custom<UploadedFile | null>().optional().default(null),
      helpMedia: z.custom<UploadedFile | null>().optional().default(null),
      free: z.boolean().default(false),
      status: z.boolean().default(false),
    })
    .refine(
      (data) => {
        if (data.discountPrice && data.price) {
          return data.discountPrice < data.price;
        }
        return true;
      },
      {
        message: "Discount price must be less than the regular price",
        path: ["discountPrice"],
      }
    );

interface TemplateDetailFormProps {
  template?: Template;
  onSubmit: (data: TemplateFormValues) => void;
  onSaveAsDraft: (data: TemplateFormValues) => void;
}

export const TemplateDetailForm = forwardRef<
  HTMLFormElement,
  TemplateDetailFormProps
>(({ template, onSubmit, onSaveAsDraft }, ref) => {
  const [loading, setLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [titleAvailable, setTitleAvailable] = useState<boolean | null>(null);
  const [titleCheckLoading, setTitleCheckLoading] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>(
    template?.category || ""
  );
  const [selectedSubcategory, setSelectedSubcategory] = useState<string>(
    template?.subcategory || ""
  );
  const [subcategories, setSubcategories] = useState<
    { id: string; name: string }[]
  >([]);

  const { toast } = useToast();
  const router = useRouter();
  const formRef = useRef<HTMLFormElement>(null);

  const form = useForm<TemplateFormValues>({
    resolver: zodResolver(createTemplateSchema(template)),
    defaultValues: template
      ? {
          title: template.title,
          slug: template.slug,
          shortDescription: template.shortDescription,
          description: template.description,
          thumbnail: template.thumbnail,
          mediaType: template.mediaType,
          category: template.category,
          subcategory: template.subcategory,
          price: template.price,
          discountPrice: template.discountPrice,
          version: template.version,
          downloadMedia: template.downloadMedia,
          previewMedia: template.previewMedia,
          helpMedia: template.helpMedia,
          free: template.free,
          status: template.status === TemplateStatus.PUBLISHED,
        }
      : {
          title: "",
          shortDescription: "",
          description: "",
          mediaType: TemplateMediaType.PDF,
          category: "",
          subcategory: "",
          tag: TemplateTag.NONE,
          price: 0,
          value: 0,
          version: "1.0",
          free: false,
          status: false,
        },
  });

  useEffect(() => {
    if (selectedCategory) {
      const category = templateCategories.find(
        (c) => c.id === selectedCategory
      );
      setSubcategories(category?.subcategories || []);
      if (!template) {
        form.setValue("subcategory", "");
      }
    }
  }, [selectedCategory]);

  const handleFormSubmit = async (
    data: TemplateFormValues,
    isDraft: boolean = false
  ) => {
    setIsSubmitting(true);
    setLoading(true);

    try {
      if (isDraft) {
        await onSaveAsDraft(data);
      } else {
        await onSubmit(data);
      }
    } finally {
      setLoading(false);
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form
        ref={ref}
        onSubmit={form.handleSubmit((data) => handleFormSubmit(data, false))}
        className="space-y-8"
      >
        {isSubmitting && (
          <div className="absolute inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
            <div className="text-center">
              <Loader2 className="w-10 h-10 animate-spin mx-auto mb-4" />
              <p className="text-lg font-semibold">
                {template ? "Updating Template..." : "Creating Template..."}
              </p>
            </div>
          </div>
        )}

        {/* Basic Information Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileTextIcon className="w-5 h-5" />
              Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Thumbnail Upload */}
            <FormField
              control={form.control}
              name="thumbnail"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <ImageIcon className="w-4 h-4" />
                    Thumbnail
                  </FormLabel>
                  <FormControl>
                    <ThumbnailImageUploader
                      uploadedImage={field.value}
                      onImageChange={(image) => {
                        field.onChange(image);
                      }}
                      className="max-w-[300px]"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Title Field */}
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Template title"
                      {...field}
                      className="max-w-2xl"
                    />
                  </FormControl>
                  <FormMessage />
                  {titleCheckLoading && !form.formState.errors.title && (
                    <div className="flex items-center text-muted-foreground">
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Checking availability...
                    </div>
                  )}
                  {!titleCheckLoading &&
                    titleAvailable === false &&
                    !form.formState.errors.title && (
                      <div className="flex items-center text-destructive">
                        <XCircle className="w-4 h-4 mr-2" />
                        This template title is already taken.
                      </div>
                    )}
                  {!titleCheckLoading &&
                    titleAvailable === true &&
                    !form.formState.errors.title && (
                      <div className="flex items-center text-green-600">
                        <CheckCircle className="w-4 h-4 mr-2" />
                        This template title is available.
                      </div>
                    )}
                </FormItem>
              )}
            />

            {/* Short Description */}
            <FormField
              control={form.control}
              name="shortDescription"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Short Description</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Brief description of the template"
                      {...field}
                      className="max-w-2xl"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <BookOpenIcon className="w-4 h-4" />
                    Description
                  </FormLabel>
                  <FormControl>
                    <TiptapEditor
                      content={field.value || ""}
                      onContentChange={field.onChange}
                      aiContent={null}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Classification Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TagIcon className="w-5 h-5" />
              Classification
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Media Type and Tag Grid */}
            <div className="grid sm:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="mediaType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <FileIcon className="w-4 h-4" />
                      Media Type
                    </FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select media type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.values(TemplateMediaType).map((type) => (
                          <SelectItem key={type} value={type}>
                            {type.charAt(0).toUpperCase() +
                              type.slice(1).toLowerCase()}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Category and Pricing Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FolderIcon className="w-5 h-5" />
                  Category & Pricing
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="grid gap-6 sm:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="category"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-2">
                            <FolderIcon className="h-4 w-4" />
                            Category
                          </FormLabel>
                          <Select
                            onValueChange={(value) => {
                              field.onChange(value);
                              setSelectedCategory(value);
                            }}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a category" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {templateCategories.map((category) => (
                                <SelectItem
                                  key={category.id}
                                  value={category.id}
                                >
                                  {category.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Choose the main category for your template
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="subcategory"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Subcategory</FormLabel>
                          <Select
                            onValueChange={(value) => {
                              field.onChange(value);
                              setSelectedSubcategory(value);
                            }}
                            defaultValue={field.value}
                            disabled={!selectedCategory}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a subcategory" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {subcategories.map((sub) => (
                                <SelectItem key={sub.id} value={sub.id}>
                                  {sub.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Choose a more specific category
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </CardContent>
        </Card>

        {/* Pricing Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSignIcon className="w-5 h-5" />
              Pricing
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid sm:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Price</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseFloat(e.target.value))
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="discountPrice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Discount Price</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        {...field}
                        value={field.value || ""}
                        onChange={(e) => {
                          const value = e.target.value;
                          field.onChange(
                            value === "" ? null : parseFloat(value)
                          );
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Free Switch */}
            <FormField
              control={form.control}
              name="free"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Free Template</FormLabel>
                    <FormDescription>
                      Make this template available for free
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Media Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileIcon className="w-5 h-5" />
              Media Files
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Version */}
            <FormField
              control={form.control}
              name="version"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Version</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., 1.0.0"
                      {...field}
                      className="max-w-[200px]"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Media Upload Grid */}
            <div className="grid sm:grid-cols-2 gap-4">
              {/* Download Media */}
              <FormField
                control={form.control}
                name="downloadMedia"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Download className="w-4 h-4" />
                      Download Media
                    </FormLabel>
                    <FormControl>
                      <FileUploader
                        uploadedFile={field.value}
                        onFileChange={(file) => {
                          field.onChange(file);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Preview Media */}
              <FormField
                control={form.control}
                name="previewMedia"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <FileIcon className="w-4 h-4" />
                      Preview Media (Optional)
                    </FormLabel>
                    <FormControl>
                      <FileUploader
                        uploadedFile={field.value}
                        onFileChange={(file) => {
                          field.onChange(file);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Help Media */}
              <FormField
                control={form.control}
                name="helpMedia"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <HelpCircle className="w-4 h-4" />
                      Help Media (Optional)
                    </FormLabel>
                    <FormControl>
                      <FileUploader
                        uploadedFile={field.value}
                        onFileChange={(file) => {
                          field.onChange(file);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>
      </form>
    </Form>
  );
});

TemplateDetailForm.displayName = "TemplateDetailForm";
