"use client";

import { useState } from "react";
import { Shell } from "@/components/shell";
import { ContentLayout } from "@/components/admin-panel/content-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  Clock,
  DollarSign,
  Download,
  Eye,
  Tag,
  CheckCircle2,
} from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import {
  Template,
  TemplateMediaType,
  TemplateStatus,
  TemplateTag,
  TemplateApprovalStatus,
} from "@/types/template";
import { Badge } from "@/components/ui/badge";
import { ApprovalStatusBadge } from "@/components/ui/approval-status-badge";
import { ApprovalControls } from "@/components/ui/approval-controls";
import { useAuth } from "@/contexts/auth-contexts";
import { approveTemplate } from "@/lib/templates";
import { Role } from "@/types/auth";
import { toast } from "sonner";

interface TemplatePreviewPageClientProps {
  initialData: Template;
}

export default function TemplatePreviewPageClient({
  initialData,
}: TemplatePreviewPageClientProps) {
  const router = useRouter();
  const { user } = useAuth();
  const [template, setTemplate] = useState<Template>(initialData);
  const [isApprovalLoading, setIsApprovalLoading] = useState(false);

  const isAdmin = user?.role === Role.Admin;

  const handleApprovalChange = async (
    status: TemplateApprovalStatus,
    comment?: string
  ) => {
    if (!template._id) return;

    setIsApprovalLoading(true);
    try {
      const response = await approveTemplate(template._id, { status, comment });
      if (response.data) {
        setTemplate(response.data);
        toast.success("Template approval status updated successfully");
      } else {
        toast.error("Failed to update template approval status");
      }
    } catch (error) {
      console.error("Error updating template approval:", error);
      toast.error("Failed to update template approval status");
    } finally {
      setIsApprovalLoading(false);
    }
  };

  const getMediaTypeIcon = (mediaType: TemplateMediaType) => {
    switch (mediaType) {
      case TemplateMediaType.DOC:
        return "📄";
      case TemplateMediaType.EXCEL:
        return "📊";
      case TemplateMediaType.PDF:
        return "📑";
      case TemplateMediaType.PPT:
        return "📽️";
      case TemplateMediaType.TXT:
        return "📝";
      case TemplateMediaType.CSV:
        return "📈";
      case TemplateMediaType.AUDIO:
        return "🎵";
      case TemplateMediaType.IMAGE:
        return "🖼️";
      case TemplateMediaType.VIDEO:
        return "🎥";
      case TemplateMediaType.TEXT:
        return "📝";
      default:
        return "📁";
    }
  };

  return (
    <ContentLayout title="Template Preview">
      <Shell>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              className="gap-2"
              onClick={() => router.back()}
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
          </div>

          <div className="grid gap-6">
            {/* Template Header */}
            <Card>
              <CardContent className="p-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="relative aspect-video rounded-lg overflow-hidden">
                    {template.thumbnail && template.thumbnail.key && (
                      <Image
                        src={`${process.env.NEXT_PUBLIC_AWS_IMAGES_CLOUDFRONT_URL}/${template.thumbnail.key}`}
                        alt={template.title}
                        fill
                        className="object-cover"
                      />
                    )}
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <h1 className="text-2xl font-bold">{template.title}</h1>
                      <Badge variant="outline" className="gap-1">
                        {getMediaTypeIcon(template.mediaType)}
                        {template.mediaType}
                      </Badge>
                    </div>
                    <p className="text-muted-foreground">
                      {template.shortDescription}
                    </p>
                    <div className="flex items-center gap-4 flex-wrap">
                      {template.free ? (
                        <span className="text-green-600 font-semibold">
                          Free
                        </span>
                      ) : (
                        <div className="flex items-center gap-2">
                          <DollarSign className="h-4 w-4" />
                          <span className="font-semibold">
                            ${template.price}
                          </span>
                          {template.discountPrice && (
                            <span className="text-muted-foreground line-through">
                              ${template.discountPrice}
                            </span>
                          )}
                        </div>
                      )}
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        <span>
                          Created{" "}
                          {new Date(template.createdAt).toLocaleDateString()}
                        </span>
                      </div>
                      <Badge
                        variant={
                          template.status === TemplateStatus.PUBLISHED
                            ? "default"
                            : "secondary"
                        }
                      >
                        {template.status === TemplateStatus.PUBLISHED ? (
                          <CheckCircle2 className="h-3 w-3 mr-1" />
                        ) : null}
                        {template.status}
                      </Badge>
                      {template.tag !== TemplateTag.NONE && (
                        <Badge variant="outline" className="gap-1">
                          <Tag className="h-3 w-3" />
                          {template.tag}
                        </Badge>
                      )}
                      {template.approvalStatus && (
                        <ApprovalStatusBadge status={template.approvalStatus} />
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Template Details */}
            <Card>
              <CardHeader>
                <CardTitle>Template Details</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold mb-2">Description</h3>
                    <p className="text-muted-foreground whitespace-pre-wrap">
                      {template.description}
                    </p>
                  </div>
                  <Separator />
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h3 className="font-semibold mb-2">Category</h3>
                      <p className="text-muted-foreground">
                        {template.category}
                      </p>
                    </div>
                    <div>
                      <h3 className="font-semibold mb-2">Subcategory</h3>
                      <p className="text-muted-foreground">
                        {template.subcategory}
                      </p>
                    </div>
                  </div>
                  <Separator />
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h3 className="font-semibold mb-2">Version</h3>
                      <p className="text-muted-foreground">
                        {template.version}
                      </p>
                    </div>
                    <div>
                      <h3 className="font-semibold mb-2">Downloads</h3>
                      <p className="text-muted-foreground">
                        {template.downloadCount} downloads
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Preview and Download */}
            <div className="grid md:grid-cols-2 gap-6">
              {template.previewMedia && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Eye className="h-5 w-5" />
                      Preview File
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <Button
                      variant="outline"
                      className="w-full gap-2"
                      onClick={() =>
                        window.open(
                          `${process.env.NEXT_PUBLIC_AWS_FILES_CLOUDFRONT_URL}/${template.previewMedia!.key}`,
                          "_blank"
                        )
                      }
                    >
                      <Eye className="h-4 w-4" />
                      View Preview
                    </Button>
                  </CardContent>
                </Card>
              )}

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Download className="h-5 w-5" />
                    Download File
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <Button
                    variant="default"
                    className="w-full gap-2"
                    onClick={() =>
                      window.open(
                        `${process.env.NEXT_PUBLIC_AWS_FILES_CLOUDFRONT_URL}/${template.downloadMedia!.key}`,
                        "_blank"
                      )
                    }
                  >
                    <Download className="h-4 w-4" />
                    Download Template
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Admin Approval Controls */}
            {isAdmin && template.approvalStatus && (
              <ApprovalControls
                currentStatus={template.approvalStatus}
                // @ts-expect-error
                onApprove={handleApprovalChange}
                isLoading={isApprovalLoading}
              />
            )}
          </div>
        </div>
      </Shell>
    </ContentLayout>
  );
}
