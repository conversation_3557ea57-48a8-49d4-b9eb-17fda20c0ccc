import { getTemplateById } from "@/lib/templates";
import { ApiStatus } from "@/types/common";
import { Metadata } from "next";
import { notFound } from "next/navigation";
import TemplatePreviewPageClient from "./_components/template-preview-page-client";

interface PreviewTemplatePageProps {
  params: Promise<{ id: string }>;
}

export async function generateMetadata({
  params,
}: PreviewTemplatePageProps): Promise<Metadata> {
  const response = await getTemplateById((await params).id);

  if (response.status === ApiStatus.FAIL || !response.data) {
    return {
      title: "Template Not Found",
      description: "The requested template preview could not be found.",
    };
  }

  return {
    title: `Preview: ${response.data.title}`,
    description: `Preview the template content and details for: ${response.data.title}`,
  };
}

export default async function PreviewTemplatePage({
  params,
}: PreviewTemplatePageProps) {
  const response = await getTemplateById((await params).id);

  if (response.status === ApiStatus.FAIL || !response.data) {
    return notFound();
  }

  return <TemplatePreviewPageClient initialData={response.data} />;
}
