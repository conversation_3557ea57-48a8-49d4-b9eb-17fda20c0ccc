"use client";

import { useState, useEffect, useRef, forwardRef } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { templateCategories } from "@/data"; // You'll need to create this
import { useToast } from "@/hooks/use-toast";
import {
  CheckCircle,
  XCircle,
  Loader2,
  Pencil,
  FileIcon,
  Download,
  HelpCircle,
} from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { useRouter } from "next/navigation";
import TiptapEditor from "@/components/editor/editor";
import { DocumentStatus, UploadedFile } from "@/types/common";
import axios from "axios";
import Image from "next/image";
import { ThumbnailImageUploader } from "@/components/uploader/thumbnail-image-uploader";
import { FileUploader } from "@/components/uploader/file-uploader";
import {
  Template,
  TemplateStatus,
  TemplateMediaType,
  TemplateTag,
  TemplateFormValues,
} from "@/types/template";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  ImageIcon,
  FileTextIcon,
  TagIcon,
  FolderIcon,
  DollarSignIcon,
  BookOpenIcon,
} from "lucide-react";

const createTemplateSchema = (template?: Template) =>
  z
    .object({
      title: z.string().min(3, "Template title is required"),
      shortDescription: z.string().min(10, "Short description is required"),
      description: z.string().nullable(),
      thumbnail: z.custom<UploadedFile>().refine((file) => {
        if (!template) {
          return file !== null && file !== undefined;
        }
        return true;
      }, "Thumbnail is required for new templates"),
      mediaType: z.nativeEnum(TemplateMediaType),
      category: z.string().min(1, "Category is required"),
      subcategory: z.string().min(1, "Subcategory is required"),
      price: z.number().min(0, "Price must be 0 or greater"),
      discountPrice: z.number().nullable().optional(),
      version: z.string().min(1, "Version is required"),
      downloadMedia: z.custom<UploadedFile>().refine((file) => {
        if (!template) {
          return file !== null && file !== undefined;
        }
        return true;
      }, "Download media is required"),
      previewMedia: z.custom<UploadedFile | null>().optional().default(null),
      helpMedia: z.custom<UploadedFile | null>().optional().default(null),
      free: z.boolean().default(false),
      status: z.boolean().default(false),
    })
    .refine(
      (data) => {
        if (data.discountPrice && data.price) {
          return data.discountPrice < data.price;
        }
        return true;
      },
      {
        message: "Discount price must be less than the regular price",
        path: ["discountPrice"],
      }
    );

interface TemplateDetailFormProps {
  template?: Template;
  onSubmit: (data: TemplateFormValues) => void;
  onSaveAsDraft: (data: TemplateFormValues) => void;
}

export const TemplateDetailForm = forwardRef<
  HTMLFormElement,
  TemplateDetailFormProps
>(({ template, onSubmit, onSaveAsDraft }, ref) => {
  const [loading, setLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>(
    template?.category || ""
  );

  const [subcategories, setSubcategories] = useState<
    { id: string; name: string }[]
  >([]);

  const form = useForm<TemplateFormValues>({
    resolver: zodResolver(createTemplateSchema(template)),
    defaultValues: template
      ? {
          title: template.title,
          shortDescription: template.shortDescription,
          description: template.description,
          thumbnail: template.thumbnail,
          mediaType: template.mediaType,
          category: template.category,
          subcategory: template.subcategory,
          price: template.price,
          discountPrice: template.discountPrice,
          version: template.version,
          downloadMedia: template.downloadMedia,
          previewMedia: template.previewMedia,
          helpMedia: template.helpMedia,
          free: template.free,
          status: template.status === TemplateStatus.PUBLISHED,
        }
      : {
          title: "",
          shortDescription: "",
          description: "",
          mediaType: TemplateMediaType.PDF,
          category: "",
          subcategory: "",
          price: 0,
          version: "1.0",
          free: false,
          status: false,
        },
  });

  useEffect(() => {
    if (selectedCategory) {
      const category = templateCategories.find(
        (c) => c.id === selectedCategory
      );
      setSubcategories(category?.subcategories || []);
      if (!template) {
        form.setValue("subcategory", "");
      }
    }
  }, [selectedCategory]);

  const handleFormSubmit = async (
    data: TemplateFormValues,
    isDraft: boolean = false
  ) => {
    setIsSubmitting(true);
    setLoading(true);

    try {
      if (isDraft) {
        await onSaveAsDraft(data);
      } else {
        await onSubmit(data);
      }
    } finally {
      setLoading(false);
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form
        ref={ref}
        onSubmit={form.handleSubmit((data) => handleFormSubmit(data, false))}
        className="space-y-8"
      >
        {isSubmitting && (
          <div className="absolute inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
            <div className="text-center">
              <Loader2 className="w-10 h-10 animate-spin mx-auto mb-4" />
              <p className="text-lg font-semibold">
                {template ? "Updating Template..." : "Creating Template..."}
              </p>
            </div>
          </div>
        )}

        {/* Basic Information Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileTextIcon className="w-5 h-5" />
              Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Thumbnail Upload */}
            <FormField
              control={form.control}
              name="thumbnail"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <ImageIcon className="w-4 h-4" />
                    Thumbnail
                  </FormLabel>
                  <FormControl>
                    <ThumbnailImageUploader
                      uploadedImage={field.value}
                      onImageChange={(image) => {
                        field.onChange(image);
                      }}
                      className="max-w-[300px]"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Title */}
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter template title" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Short Description */}
            <FormField
              control={form.control}
              name="shortDescription"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Short Description</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter short description" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <TiptapEditor
                      content={field.value || ""}
                      aiContent={null}
                      onContentChange={field.onChange}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Category Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FolderIcon className="w-5 h-5" />
              Category & Type
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Media Type */}
            <FormField
              control={form.control}
              name="mediaType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Media Type</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select media type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.values(TemplateMediaType).map((type) => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Category */}
            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Category</FormLabel>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      setSelectedCategory(value);
                    }}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {templateCategories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Subcategory */}
            <FormField
              control={form.control}
              name="subcategory"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Subcategory</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select subcategory" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {subcategories.map((subcategory) => (
                        <SelectItem key={subcategory.id} value={subcategory.id}>
                          {subcategory.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Pricing Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSignIcon className="w-5 h-5" />
              Pricing
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Free Switch */}
            <FormField
              control={form.control}
              name="free"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Free Template</FormLabel>
                    <FormDescription>
                      Make this template available for free
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {/* Price */}
            <FormField
              control={form.control}
              name="price"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Price</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter price"
                      {...field}
                      onChange={(e) =>
                        field.onChange(parseFloat(e.target.value))
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Discount Price */}
            <FormField
              control={form.control}
              name="discountPrice"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Discount Price</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter discount price"
                      {...field}
                      value={field.value || ""}
                      onChange={(e) =>
                        field.onChange(
                          e.target.value ? parseFloat(e.target.value) : null
                        )
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Media Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpenIcon className="w-5 h-5" />
              Media
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Version */}
            <FormField
              control={form.control}
              name="version"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Version</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter version" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Download Media */}
            <FormField
              control={form.control}
              name="downloadMedia"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Download Media</FormLabel>
                  <FormControl>
                    <FileUploader
                      uploadedFile={field.value}
                      onFileChange={(file) => {
                        field.onChange(file);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Preview Media */}
            <FormField
              control={form.control}
              name="previewMedia"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Preview Media</FormLabel>
                  <FormControl>
                    <FileUploader
                      uploadedFile={field.value}
                      onFileChange={(file) => {
                        field.onChange(file);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Help Media */}
            <FormField
              control={form.control}
              name="helpMedia"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Help Media</FormLabel>
                  <FormControl>
                    <FileUploader
                      uploadedFile={field.value}
                      onFileChange={(file) => {
                        field.onChange(file);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>
      </form>
    </Form>
  );
});

TemplateDetailForm.displayName = "TemplateDetailForm";
