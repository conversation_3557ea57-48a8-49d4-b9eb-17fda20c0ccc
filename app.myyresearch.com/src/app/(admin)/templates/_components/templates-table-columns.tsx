"use client";

import * as React from "react";
import { type DataTableRowAction } from "@/types";
import { type ColumnDef } from "@tanstack/react-table";
import { Ellipsis, ImageIcon } from "lucide-react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import Image from "next/image";

import {
  type Template,
  TemplateMediaType,
  TemplateStatus,
  TemplateTag,
  TemplateApprovalStatus,
} from "@/types/template";
import { type UploadedFile } from "@/types/common";
import { getErrorMessage } from "@/lib/data-table/handle-error";
import { formatDate } from "@/lib/data-table/utils";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { ApprovalStatusBadge } from "@/components/ui/approval-status-badge";

import { updateTemplate } from "../_lib/actions";
import {
  MinimalisticTemplateDto,
  CreatorType,
  approveTemplate,
} from "@/lib/templates";

interface GetColumnsProps {
  setRowAction: React.Dispatch<
    React.SetStateAction<DataTableRowAction<MinimalisticTemplateDto> | null>
  >;
}

export function getColumns({
  setRowAction,
}: GetColumnsProps): ColumnDef<MinimalisticTemplateDto>[] {
  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          className="translate-y-0.5"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="translate-y-0.5"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "title",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Title" />
      ),
      cell: ({ row }) => {
        const thumbnail = row.original.thumbnail;
        const isInstructor =
          row.original.creatorType === CreatorType.INSTRUCTOR;
        const approvalStatus = row.original.approvalStatus;

        return (
          <div className="flex items-center gap-3">
            <div className="relative w-32 min-w-32">
              <div className="aspect-[16/9] overflow-hidden rounded-md relative">
                <Image
                  src={`${process.env.NEXT_PUBLIC_AWS_IMAGES_CLOUDFRONT_URL}/${thumbnail}`}
                  alt={`${row.getValue("title")} thumbnail`}
                  fill
                  className="object-cover"
                  sizes="(max-width: 128px) 100vw, 128px"
                />
              </div>
            </div>
            <div className="min-w-[200px] flex-1">
              <div className="truncate font-medium">
                {row.getValue("title")}
              </div>
              {isInstructor && approvalStatus && (
                <div className="mt-1">
                  <ApprovalStatusBadge status={approvalStatus} />
                </div>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "publicId",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Public ID" />
      ),
      cell: ({ row }) => (
        <div className="w-[8rem] truncate">{row.getValue("publicId")}</div>
      ),
    },
    {
      accessorKey: "shortDescription",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Description" />
      ),
      cell: ({ row }) => (
        <div className="w-[15rem] truncate">
          {row.getValue("shortDescription")}
        </div>
      ),
    },
    {
      accessorKey: "category",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Category" />
      ),
      cell: ({ row }) => (
        <Badge variant="outline" className="capitalize">
          {row.getValue("category")}
        </Badge>
      ),
      filterFn: (row, id, value) => {
        return Array.isArray(value) && value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "subcategory",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Subcategory" />
      ),
      cell: ({ row }) => (
        <Badge variant="outline" className="capitalize">
          {row.getValue("subcategory")}
        </Badge>
      ),
    },
    {
      accessorKey: "mediaType",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Media Type" />
      ),
      cell: ({ row }) => {
        const mediaType = row.getValue("mediaType") as TemplateMediaType;
        return (
          <Badge variant="outline" className="capitalize">
            {mediaType.toLowerCase()}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return Array.isArray(value) && value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "tag",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Tag" />
      ),
      cell: ({ row }) => {
        const tag = row.getValue("tag") as TemplateTag;
        return (
          <Badge variant="outline" className="capitalize">
            {tag.toLowerCase()}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return Array.isArray(value) && value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Status" />
      ),
      cell: ({ row }) => {
        const status = row.getValue("status") as TemplateStatus;
        return (
          <Badge
            variant={status === "published" ? "default" : "secondary"}
            className="capitalize"
          >
            {status.toLowerCase()}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return Array.isArray(value) && value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "price",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Price" />
      ),
      cell: ({ row }) => {
        const price = row.getValue<number>("price");
        const discountPrice = row.original.discountPrice;
        const isFree = row.original.free;

        if (isFree) {
          return <div className="w-[8rem]">Free</div>;
        }

        return (
          <div className="w-[8rem]">
            {discountPrice ? (
              <div className="flex flex-col">
                <span className="line-through text-muted-foreground">
                  ${price.toFixed(2)}
                </span>
                <span>${discountPrice.toFixed(2)}</span>
              </div>
            ) : (
              <span>${price.toFixed(2)}</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "downloadCount",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Downloads" />
      ),
      cell: ({ row }) => row.getValue("downloadCount"),
    },
    {
      accessorKey: "creatorType",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Creator Type" />
      ),
      cell: ({ row }) => {
        const creatorType = row.getValue("creatorType") as CreatorType;
        return (
          <Badge variant="outline" className="capitalize">
            {creatorType.toLowerCase()}
          </Badge>
        );
      },
    },
    {
      accessorKey: "instructorUsername",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Instructor" />
      ),
      cell: ({ row }) => row.getValue("instructorUsername") || "N/A",
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Created At" />
      ),
      cell: ({ row }) => formatDate(row.getValue("createdAt")),
    },
    {
      accessorKey: "updatedAt",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Updated At" />
      ),
      cell: ({ row }) => formatDate(row.getValue("updatedAt")),
    },
    {
      id: "actions",
      cell: function Cell({ row }) {
        const router = useRouter();
        const [isUpdatePending, startUpdateTransition] = React.useTransition();
        const [isApprovalPending, startApprovalTransition] =
          React.useTransition();

        const isInstructor =
          row.original.creatorType === CreatorType.INSTRUCTOR;

        const handleApprovalChange = (status: TemplateApprovalStatus) => {
          startApprovalTransition(() => {
            toast.promise(approveTemplate(row.original._id!, { status }), {
              loading: "Updating approval status...",
              success: "Approval status updated",
              error: (err) => getErrorMessage(err),
            });
          });
        };

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                aria-label="Open menu"
                variant="ghost"
                className="flex size-8 p-0 data-[state=open]:bg-muted"
              >
                <Ellipsis className="size-4" aria-hidden="true" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-40">
              <DropdownMenuItem
                onSelect={() =>
                  router.push(`/templates/edit/${row.original._id}`)
                }
              >
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onSelect={() =>
                  router.push(`/templates/preview/${row.original._id}`)
                }
              >
                Preview
              </DropdownMenuItem>
              <DropdownMenuSeparator />

              {/* Approval actions for instructor templates */}
              {isInstructor && (
                <>
                  <DropdownMenuSub>
                    <DropdownMenuSubTrigger>
                      Approval Status
                    </DropdownMenuSubTrigger>
                    <DropdownMenuSubContent>
                      <DropdownMenuRadioGroup
                        value={row.original.approvalStatus}
                        onValueChange={(value) => {
                          handleApprovalChange(value as TemplateApprovalStatus);
                        }}
                      >
                        {Object.values(TemplateApprovalStatus).map((status) => (
                          <DropdownMenuRadioItem
                            key={status}
                            value={status}
                            className="capitalize"
                            disabled={isApprovalPending}
                          >
                            {status.toLowerCase()}
                          </DropdownMenuRadioItem>
                        ))}
                      </DropdownMenuRadioGroup>
                    </DropdownMenuSubContent>
                  </DropdownMenuSub>
                  <DropdownMenuSeparator />
                </>
              )}

              <DropdownMenuSub>
                <DropdownMenuSubTrigger>Media Type</DropdownMenuSubTrigger>
                <DropdownMenuSubContent>
                  <DropdownMenuRadioGroup
                    value={row.original.mediaType}
                    onValueChange={(value) => {
                      startUpdateTransition(() => {
                        toast.promise(
                          updateTemplate({
                            id: row.original._id!,
                            mediaType: value as TemplateMediaType,
                          }),
                          {
                            loading: "Updating...",
                            success: "Media type updated",
                            error: (err) => getErrorMessage(err),
                          }
                        );
                      });
                    }}
                  >
                    {Object.values(TemplateMediaType).map((type) => (
                      <DropdownMenuRadioItem
                        key={type}
                        value={type}
                        className="capitalize"
                        disabled={isUpdatePending}
                      >
                        {type.toLowerCase()}
                      </DropdownMenuRadioItem>
                    ))}
                  </DropdownMenuRadioGroup>
                </DropdownMenuSubContent>
              </DropdownMenuSub>
              <DropdownMenuSub>
                <DropdownMenuSubTrigger>Tag</DropdownMenuSubTrigger>
                <DropdownMenuSubContent>
                  <DropdownMenuRadioGroup
                    value={row.original.tag}
                    onValueChange={(value) => {
                      startUpdateTransition(() => {
                        toast.promise(
                          updateTemplate({
                            id: row.original._id!,
                            tag: value as TemplateTag,
                          }),
                          {
                            loading: "Updating...",
                            success: "Tag updated",
                            error: (err) => getErrorMessage(err),
                          }
                        );
                      });
                    }}
                  >
                    {Object.values(TemplateTag).map((tag) => (
                      <DropdownMenuRadioItem
                        key={tag}
                        value={tag}
                        className="capitalize"
                        disabled={isUpdatePending}
                      >
                        {tag.toLowerCase()}
                      </DropdownMenuRadioItem>
                    ))}
                  </DropdownMenuRadioGroup>
                </DropdownMenuSubContent>
              </DropdownMenuSub>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onSelect={() => setRowAction({ row, type: "delete" })}
                className="text-destructive focus:text-destructive"
              >
                Delete
                <DropdownMenuShortcut>⌘⌫</DropdownMenuShortcut>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];
}
