"use client";

import { type Table } from "@tanstack/react-table";
import { Download, Plus } from "lucide-react";
import Link from "next/link";

import { type Template } from "@/types/template";
import { MinimalisticTemplateDto } from "@/lib/templates";
import { exportTableToCSV } from "@/lib/data-table/export";
import { Button } from "@/components/ui/button";

import { DeleteTemplatesDialog } from "./delete-templates-dialog";

interface TemplatesTableToolbarActionsProps {
  table: Table<MinimalisticTemplateDto>;
  onRefresh?: () => void;
}

export function TemplatesTableToolbarActions({
  table,
  onRefresh,
}: TemplatesTableToolbarActionsProps) {
  return (
    <div className="flex items-center gap-2">
      {table.getFilteredSelectedRowModel().rows.length > 0 ? (
        <DeleteTemplatesDialog
          templates={table
            .getFilteredSelectedRowModel()
            .rows.map((row) => row.original)}
          onSuccess={() => {
            table.toggleAllRowsSelected(false);
            onRefresh?.();
          }}
        />
      ) : null}
      <Button variant="default" size="sm" className="gap-2" asChild>
        <Link href="/templates/add">
          <Plus className="size-4" aria-hidden="true" />
          Add Template
        </Link>
      </Button>
      <Button
        variant="outline"
        size="sm"
        onClick={() =>
          exportTableToCSV(table, {
            filename: "templates",
            excludeColumns: ["select", "actions"],
          })
        }
        className="gap-2"
      >
        <Download className="size-4" aria-hidden="true" />
        Export
      </Button>
    </div>
  );
}
