"use client";

import * as React from "react";
import { type Row } from "@tanstack/react-table";
import { Loader, Trash } from "lucide-react";
import { toast } from "sonner";

import { type Template } from "@/types/template";
import { MinimalisticTemplateDto } from "@/lib/templates";
import { useMediaQuery } from "@/hooks/data-table/use-media-query";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { ApiStatus } from "@/types/common";
import { deleteTemplates } from "../_lib/actions";

interface DeleteTemplatesDialogProps
  extends React.ComponentPropsWithoutRef<typeof Dialog> {
  templates: Row<MinimalisticTemplateDto>["original"][];
  showTrigger?: boolean;
  onSuccess?: () => void;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function DeleteTemplatesDialog({
  templates,
  showTrigger = true,
  onSuccess,
  open: openProp,
  onOpenChange: onOpenChangeProp,
  ...props
}: DeleteTemplatesDialogProps) {
  const [internalOpen, setInternalOpen] = React.useState(false);
  const [isPending, startTransition] = React.useTransition();

  const isDesktop = useMediaQuery("(min-width: 768px)");

  // Use external state if provided, otherwise use internal state
  const open = openProp ?? internalOpen;
  const onOpenChange = onOpenChangeProp ?? setInternalOpen;

  function onDelete() {
    startTransition(async () => {
      try {
        const { error } = await deleteTemplates({
          ids: templates.map((template) => template._id!),
        });

        if (error) {
          toast.error(error);
          return;
        }

        onOpenChange(false);
        onSuccess?.();
        toast.success("Templates deleted successfully");
      } catch (error) {
        toast.error("Something went wrong");
      }
    });
  }

  if (isDesktop) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange} {...props}>
        {showTrigger && (
          <DialogTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="gap-2 text-destructive"
            >
              <Trash className="size-4" aria-hidden="true" />
              Delete
            </Button>
          </DialogTrigger>
        )}
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Templates</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete {templates.length} template(s)?
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline" disabled={isPending}>
                Cancel
              </Button>
            </DialogClose>
            <Button
              variant="destructive"
              disabled={isPending}
              onClick={() => onDelete()}
            >
              {isPending && (
                <Loader
                  className="mr-2 size-4 animate-spin"
                  aria-hidden="true"
                />
              )}
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      {showTrigger && (
        <DrawerTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="gap-2 text-destructive"
          >
            <Trash className="size-4" aria-hidden="true" />
            Delete
          </Button>
        </DrawerTrigger>
      )}
      <DrawerContent>
        <DrawerHeader>
          <DrawerTitle>Delete Templates</DrawerTitle>
          <DrawerDescription>
            Are you sure you want to delete {templates.length} template(s)? This
            action cannot be undone.
          </DrawerDescription>
        </DrawerHeader>
        <DrawerFooter>
          <DrawerClose asChild>
            <Button variant="outline" disabled={isPending}>
              Cancel
            </Button>
          </DrawerClose>
          <Button
            variant="destructive"
            disabled={isPending}
            onClick={() => onDelete()}
          >
            {isPending && (
              <Loader className="mr-2 size-4 animate-spin" aria-hidden="true" />
            )}
            Delete
          </Button>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}
