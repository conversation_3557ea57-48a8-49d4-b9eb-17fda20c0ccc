"use client";

import * as React from "react";
import type {
  DataTableAdvancedFilterField,
  DataTableFilterField,
  DataTableRowAction,
} from "@/types";

import {
  TemplateMediaType,
  TemplateStatus,
  TemplateTag,
} from "@/types/template";
import { toSentenceCase } from "@/lib/data-table/utils";
import { useDataTable } from "@/hooks/data-table/use-data-table";
import { DataTable } from "@/components/data-table/data-table";
import { DataTableAdvancedToolbar } from "@/components/data-table/data-table-advanced-toolbar";
import { DataTableToolbar } from "@/components/data-table/data-table-toolbar";

import type {
  getTemplates,
  getTemplateCategoryCounts,
  getTemplateMediaTypeCounts,
  getTemplateTagCounts,
} from "../_lib/queries";
import { DeleteTemplatesDialog } from "./delete-templates-dialog";
import { getColumns } from "./templates-table-columns";
import { TemplatesTableFloatingBar } from "./templates-table-floating-bar";
import { TemplatesTableToolbarActions } from "./templates-table-toolbar-actions";
import { useRouter } from "next/navigation";
import { MinimalisticTemplateDto } from "@/lib/templates";

interface TemplatesTableProps {
  promises: Promise<
    [
      Awaited<ReturnType<typeof getTemplates>>,
      Awaited<ReturnType<typeof getTemplateCategoryCounts>>,
      Awaited<ReturnType<typeof getTemplateMediaTypeCounts>>,
      Awaited<ReturnType<typeof getTemplateTagCounts>>,
    ]
  >;
}

export function TemplatesTable({ promises }: TemplatesTableProps) {
  const router = useRouter();

  const [{ data, pageCount }, categoryCounts, mediaTypeCounts, tagCounts] =
    React.use(promises);

  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<MinimalisticTemplateDto> | null>(null);

  const columns = React.useMemo(
    () => getColumns({ setRowAction }),
    [setRowAction]
  );

  const filterFields: DataTableFilterField<MinimalisticTemplateDto>[] = [
    {
      id: "title",
      label: "Title",
      placeholder: "Filter titles...",
    },
    {
      id: "category",
      label: "Category",
      options: Object.entries(categoryCounts).map(([category, count]) => ({
        label: toSentenceCase(category),
        value: category,
        count,
      })),
    },
    {
      id: "mediaType",
      label: "Media Type",
      options: Object.entries(mediaTypeCounts).map(([type, count]) => ({
        label: toSentenceCase(type),
        value: type,
        count,
      })),
    },
    {
      id: "tag",
      label: "Tag",
      options: Object.entries(tagCounts).map(([tag, count]) => ({
        label: toSentenceCase(tag),
        value: tag,
        count,
      })),
    },
  ];

  const advancedFilterFields: DataTableAdvancedFilterField<MinimalisticTemplateDto>[] =
    [
      {
        id: "title",
        label: "Title",
        type: "text",
      },
      {
        id: "category",
        label: "Category",
        type: "multi-select",
        options: Object.entries(categoryCounts).map(([category, count]) => ({
          label: toSentenceCase(category),
          value: category,
          count,
        })),
      },
      {
        id: "mediaType",
        label: "Media Type",
        type: "multi-select",
        options: Object.values(TemplateMediaType).map((type) => ({
          label: toSentenceCase(type),
          value: type,
          count: mediaTypeCounts[type] || 0,
        })),
      },
      {
        id: "tag",
        label: "Tag",
        type: "multi-select",
        options: Object.values(TemplateTag).map((tag) => ({
          label: toSentenceCase(tag),
          value: tag,
          count: tagCounts[tag] || 0,
        })),
      },
      {
        id: "status",
        label: "Status",
        type: "multi-select",
        options: Object.values(TemplateStatus).map((status) => ({
          label: toSentenceCase(status),
          value: status,
          count: 0,
        })),
      },
      {
        id: "createdAt",
        label: "Created at",
        type: "date",
      },
    ];

  const { table } = useDataTable<MinimalisticTemplateDto>({
    data: data.templates,
    columns,
    pageCount,
    filterFields,
    enableAdvancedFilter: true,
    initialState: {
      sorting: [{ id: "createdAt", desc: true }],
      columnPinning: { right: ["actions"] },
    },
    getRowId: (originalRow) => originalRow._id,
    shallow: false,
    clearOnDefault: true,
  });

  return (
    <>
      <DataTable<MinimalisticTemplateDto>
        table={table}
        floatingBar={<TemplatesTableFloatingBar table={table} />}
      >
        <DataTableAdvancedToolbar<MinimalisticTemplateDto>
          table={table}
          filterFields={advancedFilterFields}
          shallow={false}
        >
          <TemplatesTableToolbarActions
            table={table}
            onRefresh={() => router.refresh()}
          />
        </DataTableAdvancedToolbar>
      </DataTable>
      <DeleteTemplatesDialog
        open={rowAction?.type === "delete"}
        onOpenChange={() => setRowAction(null)}
        templates={rowAction?.row.original ? [rowAction?.row.original] : []}
        showTrigger={false}
        onSuccess={() => rowAction?.row.toggleSelected(false)}
      />
    </>
  );
}
