import {
  createSearchParamsCache,
  parseAsArrayOf,
  parseAsInteger,
  parseAsString,
  parseAsStringEnum,
} from "nuqs/server";
import * as z from "zod";

import {
  type Template,
  TemplateMediaType,
  TemplateStatus,
  TemplateTag,
} from "@/types/template";
import {
  getFiltersStateParser,
  getSortingStateParser,
} from "@/lib/data-table/parsers";

export const searchParamsCache = createSearchParamsCache({
  flags: parseAsArrayOf(z.enum(["advancedTable", "floatingBar"])).withDefault(
    []
  ),
  page: parseAsInteger.withDefault(1),
  perPage: parseAsInteger.withDefault(10),
  sort: getSortingStateParser<Template>().withDefault([
    { id: "createdAt", desc: true },
  ]),
  title: parseAsString.withDefault(""),
  category: parseAsArrayOf(z.string()).withDefault([]),
  mediaType: parseAsArrayOf(z.nativeEnum(TemplateMediaType)).withDefault([]),
  tag: parseAsArrayOf(z.nativeEnum(TemplateTag)).withDefault([]),
  status: parseAsArrayOf(z.nativeEnum(TemplateStatus)).withDefault([]),
  from: parseAsString.withDefault(""),
  to: parseAsString.withDefault(""),
  // advanced filter
  filters: getFiltersStateParser().withDefault([]),
  joinOperator: parseAsStringEnum(["and", "or"]).withDefault("and"),
});

export const updateTemplateSchema = z.object({
  title: z.string().optional(),
  shortDescription: z.string().optional(),
  description: z.string().optional(),
  category: z.string().optional(),
  subcategory: z.string().optional(),
  mediaType: z.nativeEnum(TemplateMediaType).optional(),
  tag: z.nativeEnum(TemplateTag).optional(),
  status: z.nativeEnum(TemplateStatus).optional(),
  price: z.number().optional(),
  discountPrice: z.number().optional(),
  value: z.number().optional(),
  free: z.boolean().optional(),
});

export type GetTemplatesSchema = Awaited<
  ReturnType<typeof searchParamsCache.parse>
>;

export type UpdateTemplateSchema = z.infer<typeof updateTemplateSchema>;
