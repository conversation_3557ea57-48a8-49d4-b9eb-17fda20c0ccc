"use server";

import { type MinimalisticTemplateDto } from "@/lib/templates";
import { type GetTemplatesSchema } from "./validations";
import {
  getTemplates as getTemplatesApi,
  getTemplateCategoryCounts as getTemplateCategoryCountsApi,
  getTemplateMediaTypeCounts as getTemplateMediaTypeCountsApi,
  getTemplateTagCounts as getTemplateTagCountsApi,
} from "@/lib/templates";
import { ApiStatus } from "@/types/common";

export async function getTemplates(input: GetTemplatesSchema): Promise<{
  data: {
    templates: MinimalisticTemplateDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
  pageCount: number;
}> {
  try {
    const params = {
      search: input.title,
      category: input.category.length > 0 ? input.category[0] : undefined,
      mediaType: input.mediaType.length > 0 ? input.mediaType[0] : undefined,
      tag: input.tag.length > 0 ? input.tag[0] : undefined,
      status: input.status.length > 0 ? input.status[0] : undefined,
      limit: input.perPage || 10,
      page: input.page || 1,
      filters: input.filters,
      from: input.from || undefined,
      to: input.to || undefined,
    };

    const response = await getTemplatesApi(params);

    if (response.status === ApiStatus.SUCCESS && response.data) {
      return {
        data: response.data,
        pageCount: response.data.totalPages,
      };
    }

    return {
      data: {
        templates: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      },
      pageCount: 0,
    };
  } catch {
    return {
      data: {
        templates: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      },
      pageCount: 0,
    };
  }
}

export async function getTemplateCategoryCounts(
  input: GetTemplatesSchema
): Promise<Record<string, number>> {
  try {
    const params = {
      search: input.title,
      category: input.category.length > 0 ? input.category[0] : undefined,
      mediaType: input.mediaType.length > 0 ? input.mediaType[0] : undefined,
      tag: input.tag.length > 0 ? input.tag[0] : undefined,
      status: input.status.length > 0 ? input.status[0] : undefined,
      limit: input.perPage,
      page: input.page,
      from: input.from || undefined,
      to: input.to || undefined,
    };
    const response = await getTemplateCategoryCountsApi(params);
    if (response.status === ApiStatus.SUCCESS && response.data) {
      return response.data.counts;
    }
    return {};
  } catch {
    return {};
  }
}

export async function getTemplateMediaTypeCounts(
  input: GetTemplatesSchema
): Promise<Record<string, number>> {
  try {
    const params = {
      search: input.title,
      category: input.category.length > 0 ? input.category[0] : undefined,
      mediaType: input.mediaType.length > 0 ? input.mediaType[0] : undefined,
      tag: input.tag.length > 0 ? input.tag[0] : undefined,
      status: input.status.length > 0 ? input.status[0] : undefined,
      limit: input.perPage,
      page: input.page,
      from: input.from || undefined,
      to: input.to || undefined,
    };
    const response = await getTemplateMediaTypeCountsApi(params);
    if (response.status === ApiStatus.SUCCESS && response.data) {
      return response.data.counts;
    }
    return {};
  } catch {
    return {};
  }
}

export async function getTemplateTagCounts(
  input: GetTemplatesSchema
): Promise<Record<string, number>> {
  try {
    const params = {
      search: input.title,
      category: input.category.length > 0 ? input.category[0] : undefined,
      mediaType: input.mediaType.length > 0 ? input.mediaType[0] : undefined,
      tag: input.tag.length > 0 ? input.tag[0] : undefined,
      status: input.status.length > 0 ? input.status[0] : undefined,
      limit: input.perPage,
      page: input.page,
      from: input.from || undefined,
      to: input.to || undefined,
    };
    const response = await getTemplateTagCountsApi(params);
    if (response.status === ApiStatus.SUCCESS && response.data) {
      return response.data.counts;
    }
    return {};
  } catch {
    return {};
  }
}
