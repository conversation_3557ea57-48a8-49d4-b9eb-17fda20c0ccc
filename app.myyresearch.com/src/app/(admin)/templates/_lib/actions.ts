"use server";

import { revalidateTag, unstable_noStore } from "next/cache";
import { getErrorMessage } from "@/lib/data-table/handle-error";
import {
  updateTemplates as updateTemplatesApi,
  deleteTemplate as deleteTemplateApi,
} from "@/lib/templates";
import type { UpdateTemplateSchema } from "./validations";
import {
  TemplateMediaType,
  TemplateStatus,
  TemplateTag,
} from "@/types/template";

export async function updateTemplate(
  input: UpdateTemplateSchema & { id: string }
) {
  unstable_noStore();
  try {
    const response = await updateTemplatesApi([input.id], input);

    revalidateTag("templates");
    revalidateTag("template-category-counts");
    revalidateTag("template-media-type-counts");
    revalidateTag("template-tag-counts");

    return {
      data: response.data?.[0],
      error: null,
    };
  } catch (err) {
    return {
      data: null,
      error: getErrorMessage(err),
    };
  }
}

export async function updateTemplates(input: {
  ids: string[];
  category?: string;
  mediaType?: TemplateMediaType;
  tag?: TemplateTag;
  status?: TemplateStatus;
  price?: number;
  discountPrice?: number;
  value?: number;
  free?: boolean;
}) {
  unstable_noStore();
  try {
    const response = await updateTemplatesApi(input.ids, {
      category: input.category,
      mediaType: input.mediaType,
      tag: input.tag,
      status: input.status,
      price: input.price,
      discountPrice: input.discountPrice,
      value: input.value,
      free: input.free,
    });

    revalidateTag("templates");
    revalidateTag("template-category-counts");
    revalidateTag("template-media-type-counts");
    revalidateTag("template-tag-counts");

    return {
      data: response.data,
      error: null,
    };
  } catch (err) {
    return {
      data: null,
      error: getErrorMessage(err),
    };
  }
}

export async function deleteTemplate(input: { id: string }) {
  unstable_noStore();
  try {
    const response = await deleteTemplateApi(input.id);

    revalidateTag("templates");
    revalidateTag("template-category-counts");
    revalidateTag("template-media-type-counts");
    revalidateTag("template-tag-counts");

    return {
      data: response.data,
      error: null,
    };
  } catch (err) {
    return {
      data: null,
      error: getErrorMessage(err),
    };
  }
}

export async function deleteTemplates(input: { ids: string[] }) {
  unstable_noStore();
  try {
    const response = await deleteTemplateApi(input.ids[0]);

    revalidateTag("templates");
    revalidateTag("template-category-counts");
    revalidateTag("template-media-type-counts");
    revalidateTag("template-tag-counts");

    return {
      data: response.data,
      error: null,
    };
  } catch (err) {
    return {
      data: null,
      error: getErrorMessage(err),
    };
  }
}
