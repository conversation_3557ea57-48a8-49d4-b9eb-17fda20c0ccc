import * as React from "react";
import { type SearchParams } from "@/types";
import { ContentLayout } from "@/components/admin-panel/content-layout";
import Link from "next/link";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { DataTableSkeleton } from "@/components/data-table/data-table-skeleton";
import { Shell } from "@/components/shell";

import { TemplatesTable } from "./_components/templates-table";
import {
  getTemplates,
  getTemplateCategoryCounts,
  getTemplateMediaTypeCounts,
  getTemplateTagCounts,
} from "./_lib/queries";
import { searchParamsCache } from "./_lib/validations";
import { getValidFilters } from "@/lib/data-table/data-table";
import { type Metadata } from "next";

export const metadata: Metadata = {
  title: "Templates",
  description: "Manage your templates",
};

interface IndexPageProps {
  searchParams: Promise<SearchParams>;
}

export default async function IndexPage(props: IndexPageProps) {
  const searchParams = await props.searchParams;
  const search = searchParamsCache.parse(searchParams);
  const validFilters = getValidFilters(search.filters);
  const promises = Promise.all([
    getTemplates({
      ...search,
      filters: validFilters,
    }),
    getTemplateCategoryCounts({
      ...search,
      filters: validFilters,
    }),
    getTemplateMediaTypeCounts({
      ...search,
      filters: validFilters,
    }),
    getTemplateTagCounts({
      ...search,
      filters: validFilters,
    }),
  ]);

  return (
    <ContentLayout title="Templates">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">Home</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Templates</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <Shell className="gap-2">
        <React.Suspense
          fallback={
            <DataTableSkeleton
              columnCount={8}
              searchableColumnCount={1}
              filterableColumnCount={4}
              cellWidths={[
                "10rem",
                "15rem",
                "12rem",
                "12rem",
                "12rem",
                "12rem",
                "8rem",
                "12rem",
                "8rem",
              ]}
              shrinkZero
            />
          }
        >
          <TemplatesTable promises={promises} />
        </React.Suspense>
      </Shell>
    </ContentLayout>
  );
}
