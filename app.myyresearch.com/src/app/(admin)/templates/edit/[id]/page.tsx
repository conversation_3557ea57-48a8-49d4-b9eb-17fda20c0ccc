import { getTemplateById } from "@/lib/templates";
import { ApiStatus } from "@/types/common";
import { EditTemplateContent } from "@/app/(admin)/templates/_components/edit-template-content";
import { Metadata } from "next";
import { notFound } from "next/navigation";

interface EditTemplatePageProps {
  params: Promise<{ id: string }>;
}

export async function generateMetadata({
  params,
}: EditTemplatePageProps): Promise<Metadata> {
  const response = await getTemplateById((await params).id);

  if (response.status === ApiStatus.FAIL || !response.data) {
    return {
      title: "Template Not Found",
      description: "The requested template could not be found.",
    };
  }

  return {
    title: `Edit ${response.data.title}`,
    description: `Edit and update details for the template: ${response.data.title}`,
  };
}

export default async function EditTemplatePage({
  params,
}: EditTemplatePageProps) {
  const response = await getTemplateById((await params).id);

  if (response.status === ApiStatus.FAIL || !response.data) {
    return notFound();
  }

  return <EditTemplateContent template={response.data} />;
}
