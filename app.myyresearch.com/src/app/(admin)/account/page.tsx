import Link from "next/link";
import { Metadata } from "next";
import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  Bread<PERSON>rumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import UpdateProfileForm from "./_components/UpdateProfileForm";
import PasswordUpdateForm from "./_components/PasswordUpdateForm";
import TwoFactorCard from "./_components/TwoFactorCard";

export const metadata: Metadata = {
  title: "Account Settings",
  description: "Manage your account settings, security, and preferences",
};

export default async function Settings() {
  return (
    <ContentLayout title="Security Settings">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">Home</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Settings</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="max-w-3xl space-y-6 mt-5">
        <UpdateProfileForm />
        <PasswordUpdateForm />
        <TwoFactorCard />
      </div>
    </ContentLayout>
  );
}
