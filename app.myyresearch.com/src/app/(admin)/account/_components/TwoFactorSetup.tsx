"use client";
import { useState } from "react";
import { enable2FA, generate2FA } from "@/lib/authenticaton";
import { ApiError } from "@/types/error";
import { ApiStatus } from "@/types/common";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface Props {
  onComplete: () => void;
  onCancel: () => void;
}

export default function TwoFactorSetup({ onComplete, onCancel }: Props) {
  const [qrCode, setQrCode] = useState("");
  const [secret, setSecret] = useState("");
  const [code, setCode] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [step, setStep] = useState<"generate" | "verify">("generate");

  const handleGenerate = async () => {
    setLoading(true);
    setError("");

    try {
      const response = await generate2FA();
      if (response.status === ApiStatus.SUCCESS && response.data) {
        const { qrCode: newQrCode, secret: newSecret } = response.data;
        setQrCode(newQrCode);
        setSecret(newSecret);
        setStep("verify");
      } else {
        setError(response.message || "Failed to generate 2FA");
      }
    } catch (err: unknown) {
      const apiError = err as ApiError;
      setError(apiError.response?.data?.message || "Failed to generate 2FA");
    } finally {
      setLoading(false);
    }
  };

  const handleVerify = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      const response = await enable2FA(code);
      if (response.status === ApiStatus.SUCCESS) {
        onComplete();
      } else {
        setError(response.message || "Invalid verification code");
      }
    } catch (err: unknown) {
      const apiError = err as ApiError;
      setError(apiError.response?.data?.message || "Invalid verification code");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Set up Two-Factor Authentication</CardTitle>
      </CardHeader>
      <CardContent>
        {step === "generate" ? (
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Two-factor authentication adds an extra layer of security to your
              account by requiring a code from your phone in addition to your
              password.
            </p>
            <div className="flex gap-3">
              <Button
                onClick={handleGenerate}
                disabled={loading}
                className="flex-1"
              >
                {loading ? "Generating..." : "Set up 2FA"}
              </Button>
              <Button onClick={onCancel} variant="outline">
                Cancel
              </Button>
            </div>
          </div>
        ) : (
          <form onSubmit={handleVerify} className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                1. Scan this QR code with your authenticator app:
              </p>
              {qrCode && (
                <div className="bg-background p-4 inline-block">
                  <Image
                    src={qrCode}
                    alt="2FA QR Code"
                    width={200}
                    height={200}
                    unoptimized
                  />
                </div>
              )}
            </div>

            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                2. Or manually enter this code in your app:
              </p>
              <code className="block p-2 bg-muted rounded font-mono text-sm">
                {secret}
              </code>
            </div>

            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                3. Enter the verification code from your app:
              </p>
              <Input
                type="text"
                value={code}
                onChange={(e) => setCode(e.target.value)}
                placeholder="Enter 6-digit code"
                maxLength={6}
                pattern="\d{6}"
                required
              />
            </div>

            <div className="flex gap-3">
              <Button
                type="submit"
                disabled={loading || code.length !== 6}
                className="flex-1"
              >
                {loading ? "Verifying..." : "Verify & Enable 2FA"}
              </Button>
              <Button type="button" onClick={onCancel} variant="outline">
                Cancel
              </Button>
            </div>
          </form>
        )}
      </CardContent>
    </Card>
  );
}
