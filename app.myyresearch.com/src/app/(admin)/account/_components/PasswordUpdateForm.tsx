"use client";
import { useState } from "react";
import { updateProfile } from "@/lib/authenticaton";
import { UpdateProfileData } from "@/types/auth";
import { ApiStatus } from "@/types/common";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";

export default function PasswordUpdateForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    currentPassword: "",
    newPassword: "",
  });
  const [showForm, setShowForm] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    if (!formData.currentPassword) {
      setError("Current password is required");
      setIsLoading(false);
      return;
    }

    try {
      const updateData: UpdateProfileData = {
        currentPassword: formData.currentPassword,
        newPassword: formData.newPassword,
      };

      const response = await updateProfile(updateData);

      if (response.status === ApiStatus.SUCCESS) {
        setSuccess("Password updated successfully");
        setFormData({
          currentPassword: "",
          newPassword: "",
        });
      } else {
        setError(response.message || "Failed to update password");
      }
    } catch {
      setError("Failed to update password");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Password Settings</CardTitle>
      </CardHeader>
      <CardContent>
        {showForm ? (
          <div>
            <form onSubmit={handleSubmit} className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
              {success && (
                <Alert>
                  <AlertDescription>{success}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                <label className="text-sm font-medium">Current Password</label>
                <Input
                  type="password"
                  name="currentPassword"
                  value={formData.currentPassword}
                  onChange={handleChange}
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">New Password</label>
                <Input
                  type="password"
                  name="newPassword"
                  value={formData.newPassword}
                  onChange={handleChange}
                />
              </div>

              <div className="flex space-x-2">
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? "Updating..." : "Update Password"}
                </Button>
                <Button variant="outline" onClick={() => setShowForm(false)}>
                  Cancel
                </Button>
              </div>
            </form>
          </div>
        ) : (
          <div className="space-y-4">
            <p className="text-gray-600 dark:text-gray-400">
              Update your password to maintain account security. We recommend
              using a strong, unique password.
            </p>
            <Button onClick={() => setShowForm(true)}>Update Password</Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
