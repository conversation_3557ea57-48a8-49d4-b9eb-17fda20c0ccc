"use client";

import { InstructorPaymentDetailsForm } from "@/components/account/instructor-payment-details-form";
import { InstructorPaymentSummary } from "@/components/account/instructor-payment-summary";
import { InstructorPaymentHistory } from "@/components/account/instructor-payment-history";
import { InstructorPaymentRequests } from "@/components/account/instructor-payment-requests";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CreditCard, DollarSign, History, FileText } from "lucide-react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { ContentLayout } from "@/components/admin-panel/content-layout";
import Link from "next/link";

export default function PaymentDetailsPage() {
  return (
    <ContentLayout title="Payment Details">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/admin">Dashboard</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Payment Details</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Payment Details</h1>
          <p className="text-gray-600">
            Manage your payment preferences and view your earnings
          </p>
        </div>

        <Tabs defaultValue="details" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="details" className="flex items-center gap-2">
              <CreditCard className="h-4 w-4" />
              Payment Details
            </TabsTrigger>

            <TabsTrigger value="requests" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Payment Requests
            </TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Payment Information</CardTitle>
                <CardDescription>
                  Set up your payment preferences to receive earnings from
                  course sales
                </CardDescription>
              </CardHeader>
              <CardContent>
                <InstructorPaymentDetailsForm />
              </CardContent>
            </Card>
          </TabsContent>

          {/* <TabsContent value="summary" className="space-y-6">
            <InstructorPaymentSummary />
          </TabsContent>

          <TabsContent value="history" className="space-y-6">
            <InstructorPaymentHistory />
          </TabsContent> */}

          <TabsContent value="requests" className="space-y-6">
            <InstructorPaymentRequests />
          </TabsContent>
        </Tabs>
      </div>
    </ContentLayout>
  );
}
