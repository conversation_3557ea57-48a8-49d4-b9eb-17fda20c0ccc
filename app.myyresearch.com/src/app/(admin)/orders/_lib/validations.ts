import {
  createSearchParamsCache,
  parseAsArrayOf,
  parseAsInteger,
  parseAsString,
  parseAsStringEnum,
} from "nuqs/server";
import * as z from "zod";

import { type Order, OrderStatus, PaymentStatus } from "@/types/orders";
import {
  getFiltersStateParser,
  getSortingStateParser,
} from "@/lib/data-table/parsers";

export const searchParamsCache = createSearchParamsCache({
  flags: parseAsArrayOf(z.enum(["advancedTable", "floatingBar"])).withDefault(
    []
  ),
  page: parseAsInteger.withDefault(1),
  perPage: parseAsInteger.withDefault(10),
  sort: getSortingStateParser<Order>().withDefault([
    { id: "createdAt", desc: true },
  ]),
  orderNumber: parseAsString.withDefault(""),
  status: parseAsArrayOf(parseAsStringEnum(Object.values(OrderStatus))).withDefault([]),
  paymentStatus: parseAsArrayOf(parseAsStringEnum(Object.values(PaymentStatus))).withDefault([]),
  from: parseAsString.withDefault(""),
  to: parseAsString.withDefault(""),
  userId: parseAsString.withDefault(""),
  cartId: parseAsString.withDefault(""),
  paymentIntentId: parseAsString.withDefault(""),
  // Advanced filter
  filters: getFiltersStateParser().withDefault([]),
});

export type GetOrdersSchema = Awaited<
  ReturnType<typeof searchParamsCache.parse>
>;

export const createOrderSchema = z.object({
  cartId: z.string().min(1, "Cart ID is required"),
  metadata: z.record(z.any()).optional(),
});

export const updateOrderSchema = z.object({
  status: z.nativeEnum(OrderStatus).optional(),
  paymentStatus: z.nativeEnum(PaymentStatus).optional(),
  cancellationReason: z.string().optional(),
});

export type CreateOrderSchema = z.infer<typeof createOrderSchema>;
export type UpdateOrderSchema = z.infer<typeof updateOrderSchema>;
