"use server";

import { type Order } from "@/types/orders";
import { getAdminOrders } from "@/lib/orders";
import { ApiStatus } from "@/types/common";
import { type GetOrdersSchema } from "./validations";

export async function getOrders(input: GetOrdersSchema): Promise<{
  data: {
    orders: Order[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
  pageCount: number;
}> {
  try {
    const params = {
      orderNumber: input.orderNumber,
      status: input.status.length > 0 ? input.status[0] : undefined,
      paymentStatus: input.paymentStatus.length > 0 ? input.paymentStatus[0] : undefined,
      limit: input.perPage || 10,
      page: input.page || 1,
      from: input.from || undefined,
      to: input.to || undefined,
      userId: input.userId || undefined,
      cartId: input.cartId || undefined,
      paymentIntentId: input.paymentIntentId || undefined,
    };

    const response = await getAdminOrders(params);

    if (response.status === ApiStatus.SUCCESS && response.data) {
      return {
        data: {
          orders: response.data.orders,
          total: response.data.total,
          page: response.data.page,
          limit: params.limit,
          totalPages: response.data.totalPages,
        },
        pageCount: response.data.totalPages,
      };
    }

    return {
      data: {
        orders: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      },
      pageCount: 0,
    };
  } catch (error) {
    console.error("Error fetching orders:", error);
    return {
      data: {
        orders: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      },
      pageCount: 0,
    };
  }
}

export async function getOrderStatusCounts(): Promise<
  Array<{ status: string; count: number }>
> {
  try {
    // This would ideally come from a separate API endpoint
    // For now, we'll return an empty array and populate dynamically
    return [];
  } catch (error) {
    console.error("Error fetching order status counts:", error);
    return [];
  }
}
