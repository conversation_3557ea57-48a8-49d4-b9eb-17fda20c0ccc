"use client";

import * as React from "react";
import { type ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DataTable } from "@/components/data-table/data-table";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { DataTableToolbar } from "@/components/data-table/data-table-toolbar";
import { useDataTable } from "@/hooks/data-table/use-data-table";
import { Order, OrderStatus, PaymentStatus } from "@/types/orders";
import { formatDate } from "@/lib/data-table/utils";
import { MoreH<PERSON>zontal, Eye, FileText, RefreshCw } from "lucide-react";
import { type DataTableFilterField } from "@/types";

interface OrdersTableProps {
  data: Order[];
  pageCount: number;
}

export function OrdersTable({ data, pageCount }: OrdersTableProps) {
  const columns = React.useMemo<ColumnDef<Order>[]>(
    () => [
      {
        id: "select",
        header: ({ table }) => (
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && "indeterminate")
            }
            onCheckedChange={(value) =>
              table.toggleAllPageRowsSelected(!!value)
            }
            aria-label="Select all"
            className="translate-y-0.5"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
            className="translate-y-0.5"
          />
        ),
        enableSorting: false,
        enableHiding: false,
      },
      {
        accessorKey: "orderNumber",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Order Number" />
        ),
        cell: ({ row }) => (
          <div className="font-medium">{row.getValue("orderNumber")}</div>
        ),
      },
      {
        accessorKey: "total",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Total" />
        ),
        cell: ({ row }) => {
          const total = row.getValue("total") as number;
          return <div className="font-medium">${total.toFixed(2)}</div>;
        },
      },
      {
        accessorKey: "status",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Status" />
        ),
        cell: ({ row }) => {
          const status = row.getValue("status") as OrderStatus;
          return (
            <Badge
              variant={
                status === OrderStatus.COMPLETED
                  ? "default"
                  : status === OrderStatus.CANCELLED
                    ? "destructive"
                    : status === OrderStatus.PROCESSING
                      ? "secondary"
                      : "outline"
              }
            >
              {status}
            </Badge>
          );
        },
      },
      {
        accessorKey: "paymentStatus",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Payment Status" />
        ),
        cell: ({ row }) => {
          const paymentStatus = row.getValue("paymentStatus") as PaymentStatus;
          return (
            <Badge
              variant={
                paymentStatus === PaymentStatus.COMPLETED
                  ? "default"
                  : paymentStatus === PaymentStatus.FAILED
                    ? "destructive"
                    : paymentStatus === PaymentStatus.PROCESSING
                      ? "secondary"
                      : "outline"
              }
            >
              {paymentStatus}
            </Badge>
          );
        },
      },
      {
        accessorKey: "items",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Items" />
        ),
        cell: ({ row }) => {
          const items = row.getValue("items") as any[];
          return <div className="text-sm">{items?.length || 0} items</div>;
        },
        enableSorting: false,
      },
      {
        accessorKey: "createdAt",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Created Date" />
        ),
        cell: ({ row }) => {
          const date = row.getValue("createdAt") as Date;
          return <div>{formatDate(date)}</div>;
        },
      },
      {
        accessorKey: "paidAt",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Paid Date" />
        ),
        cell: ({ row }) => {
          const date = row.getValue("paidAt") as Date;
          return <div>{date ? formatDate(date) : "-"}</div>;
        },
      },
      {
        id: "actions",
        cell: ({ row }) => {
          return (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  aria-label="Open menu"
                  variant="ghost"
                  className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
                >
                  <MoreHorizontal className="h-4 w-4" />
                  <span className="sr-only">Open menu</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-[160px]">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {/* <DropdownMenuItem>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </DropdownMenuItem> */}
                <DropdownMenuItem>
                  <FileText className="mr-2 h-4 w-4" />
                  View Invoice
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Update Status
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          );
        },
      },
    ],
    []
  );

  const filterFields: DataTableFilterField<Order>[] = [
    {
      id: "orderNumber",
      label: "Order Number",
      placeholder: "Search order numbers...",
    },
    {
      id: "status",
      label: "Status",
      options: [
        { label: "Pending", value: OrderStatus.PENDING },
        { label: "Processing", value: OrderStatus.PROCESSING },
        { label: "Completed", value: OrderStatus.COMPLETED },
        { label: "Cancelled", value: OrderStatus.CANCELLED },
        { label: "Refunded", value: OrderStatus.REFUNDED },
      ],
    },
    {
      id: "paymentStatus",
      label: "Payment Status",
      options: [
        { label: "Pending", value: PaymentStatus.PENDING },
        { label: "Processing", value: PaymentStatus.PROCESSING },
        { label: "Completed", value: PaymentStatus.COMPLETED },
        { label: "Failed", value: PaymentStatus.FAILED },
        { label: "Refunded", value: PaymentStatus.REFUNDED },
      ],
    },
  ];

  const { table } = useDataTable({
    data,
    columns,
    pageCount,
    filterFields,
    enableAdvancedFilter: false,
    initialState: {
      sorting: [{ id: "createdAt", desc: true }],
      columnPinning: { right: ["actions"] },
    },
    getRowId: (originalRow) => originalRow._id,
    shallow: false,
    clearOnDefault: true,
  });

  return (
    <DataTable table={table}>
      <DataTableToolbar table={table} filterFields={filterFields} />
    </DataTable>
  );
}
