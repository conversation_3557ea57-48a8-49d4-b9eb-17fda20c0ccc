import * as React from "react";
import Link from "next/link";
import { type SearchParams } from "@/types";

import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Skeleton } from "@/components/ui/skeleton";
import { DataTableSkeleton } from "@/components/data-table/data-table-skeleton";
import { DateRangePicker } from "@/components/date-range-picker";
import { Shell } from "@/components/shell";

import { OrdersTable } from "./_components/orders-table";
import { getOrders, getOrderStatusCounts } from "./_lib/queries";
import { searchParamsCache } from "./_lib/validations";
import { type Metadata } from "next";

export const metadata: Metadata = {
  title: "Orders",
  description: "Manage orders",
};

interface OrdersPageProps {
  searchParams: Promise<SearchParams>;
}

export default async function OrdersPage({ searchParams }: OrdersPageProps) {
  const search = searchParamsCache.parse(await searchParams);

  const promises = Promise.all([getOrders(search), getOrderStatusCounts()]);

  const [ordersResponse] = await promises;

  return (
    <ContentLayout title="Orders">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/admin">Dashboard</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Orders</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <Shell className="gap-2">
        <React.Suspense fallback={<Skeleton className="h-7 w-52" />}>
          <DateRangePicker
            triggerSize="sm"
            triggerClassName="ml-auto w-56 sm:w-60"
            align="end"
          />
        </React.Suspense>
        <React.Suspense
          fallback={
            <DataTableSkeleton
              columnCount={9}
              searchableColumnCount={1}
              filterableColumnCount={2}
              cellWidths={[
                "10rem",
                "15rem",
                "10rem",
                "8rem",
                "10rem",
                "8rem",
                "12rem",
                "12rem",
                "8rem",
              ]}
              shrinkZero
            />
          }
        >
          <OrdersTable
            data={ordersResponse.data?.orders ?? []}
            pageCount={ordersResponse.pageCount ?? 0}
          />
        </React.Suspense>
      </Shell>
    </ContentLayout>
  );
}
