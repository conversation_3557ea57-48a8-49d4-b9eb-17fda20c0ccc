"use client";

import * as React from "react";
import type {
  DataTableAdvanced<PERSON>ilterField,
  DataTableFilterField,
  DataTableRowAction,
} from "@/types";

import { UserRole, UserStatus, type User } from "@/types/user";
import { toSentenceCase } from "@/lib/data-table/utils";
import { useDataTable } from "@/hooks/data-table/use-data-table";
import { DataTable } from "@/components/data-table/data-table";
import { DataTableAdvancedToolbar } from "@/components/data-table/data-table-advanced-toolbar";
import { DataTableToolbar } from "@/components/data-table/data-table-toolbar";

import type {
  getUserRoleCounts,
  getUsers,
  getUserStatusCounts,
} from "../_lib/queries";
import { DeleteUsersDialog } from "./delete-users-dialog";
import { useFeatureFlags } from "./feature-flags-provider";
import { getColumns } from "./users-table-columns";
import { UsersTableFloatingBar } from "./users-table-floating-bar";
import { UsersTableToolbarActions } from "./users-table-toolbar-actions";
import { UpdateUserSheet } from "./update-user-sheet";
import { useRouter } from "next/navigation";

interface UsersTableProps {
  promises: Promise<
    [
      Awaited<ReturnType<typeof getUsers>>,
      Awaited<ReturnType<typeof getUserStatusCounts>>,
      Awaited<ReturnType<typeof getUserRoleCounts>>
    ]
  >;
}

export function UsersTable({ promises }: UsersTableProps) {
  const router = useRouter();
  const { featureFlags } = useFeatureFlags();

  const [{ data, pageCount }, statusCounts, roleCounts] = React.use(promises);

  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<User> | null>(null);

  const columns = React.useMemo(
    () => getColumns({ setRowAction }),
    [setRowAction]
  );

  const filterFields: DataTableFilterField<User>[] = [
    {
      id: "email",
      label: "Email",
      placeholder: "Filter emails...",
    },
    {
      id: "status",
      label: "Status",
      options: Object.values(UserStatus).map((status) => ({
        label: toSentenceCase(status),
        value: status,
        count: statusCounts[status],
      })),
    },
    {
      id: "role",
      label: "Role",
      options: Object.values(UserRole).map((role) => ({
        label: toSentenceCase(role),
        value: role,
        count: roleCounts[role],
      })),
    },
  ];

  const advancedFilterFields: DataTableAdvancedFilterField<User>[] = [
    {
      id: "email",
      label: "Email",
      type: "text",
    },
    {
      id: "status",
      label: "Status",
      type: "multi-select",
      options: Object.values(UserStatus).map((status) => ({
        label: toSentenceCase(status),
        value: status,
        count: statusCounts[status],
      })),
    },
    {
      id: "role",
      label: "Role",
      type: "multi-select",
      options: Object.values(UserRole).map((role) => ({
        label: toSentenceCase(role),
        value: role,
        count: roleCounts[role],
      })),
    },
    {
      id: "createdAt",
      label: "Created at",
      type: "date",
    },
  ];

  const enableAdvancedTable = featureFlags.includes("advancedTable");
  const enableFloatingBar = featureFlags.includes("floatingBar");

  const { table } = useDataTable({
    data,
    columns,
    pageCount,
    filterFields,
    enableAdvancedFilter: enableAdvancedTable,
    initialState: {
      sorting: [{ id: "createdAt", desc: true }],
      columnPinning: { right: ["actions"] },
    },
    getRowId: (originalRow) => originalRow._id,
    shallow: false,
    clearOnDefault: true,
  });

  return (
    <>
      <DataTable
        table={table}
        floatingBar={
          enableFloatingBar ? <UsersTableFloatingBar table={table} /> : null
        }
      >
        {enableAdvancedTable ? (
          <DataTableAdvancedToolbar
            table={table}
            filterFields={advancedFilterFields}
            shallow={false}
          >
            <UsersTableToolbarActions
              table={table}
              onRefresh={() => router.refresh()}
            />
          </DataTableAdvancedToolbar>
        ) : (
          <DataTableToolbar table={table} filterFields={filterFields}>
            <UsersTableToolbarActions
              table={table}
              onRefresh={() => router.refresh()}
            />
          </DataTableToolbar>
        )}
      </DataTable>
      <UpdateUserSheet
        open={rowAction?.type === "update"}
        onOpenChange={() => setRowAction(null)}
        user={rowAction?.row.original ?? null}
      />
      <DeleteUsersDialog
        open={rowAction?.type === "delete"}
        onOpenChange={() => setRowAction(null)}
        users={rowAction?.row.original ? [rowAction?.row.original] : []}
        showTrigger={false}
        onSuccess={() => rowAction?.row.toggleSelected(false)}
      />
    </>
  );
}
