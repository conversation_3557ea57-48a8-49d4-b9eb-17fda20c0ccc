"use server";
import { User<PERSON><PERSON>, type User, UserStatus } from "@/types/user";
import { type GetUsersSchema } from "./validations";
import { getUsers as getUsersApi } from "@/lib/users";
import { ApiStatus } from "@/types/common";
import {
  getUserStatusCounts as getUserStatusCountsApi,
  getUserRoleCounts as getUserRoleCountsApi,
} from "@/lib/users";

export async function getUsers(input: GetUsersSchema): Promise<{
  data: User[];
  pageCount: number;
}> {
  try {
    const params = {
      search: input.email,
      role: input.role.length > 0 ? (input.role[0] as UserRole) : undefined,
      status:
        input.status.length > 0 ? (input.status[0] as UserStatus) : undefined,
      limit: input.perPage || 10,
      page: input.page || 1,
      filters: input.filters,
      sort: input.sort,
      from: input.from || undefined,
      to: input.to || undefined,
    };

    const response = await getUsers<PERSON><PERSON>(params);

    if (response.status === ApiStatus.SUCCESS && response.data) {
      return {
        data: response.data.users,
        pageCount: response.data.totalPages,
      };
    }

    return { data: [], pageCount: 0 };
  } catch {
    return { data: [], pageCount: 0 };
  }
}

export async function getUserStatusCounts(
  input: GetUsersSchema
): Promise<Record<UserStatus, number>> {
  try {
    const params = {
      search: input.email,
      role: input.role.length > 0 ? (input.role[0] as UserRole) : undefined,
      status:
        input.status.length > 0 ? (input.status[0] as UserStatus) : undefined,
      limit: input.perPage,
      page: input.page,
      from: input.from || undefined,
      to: input.to || undefined,
    };
    const response = await getUserStatusCountsApi(params);
    if (response.status === ApiStatus.SUCCESS && response.data) {
      return response.data.counts;
    }
    return { active: 0, inactive: 0, suspended: 0 };
  } catch {
    return { active: 0, inactive: 0, suspended: 0 };
  }
}

export async function getUserRoleCounts(
  input: GetUsersSchema
): Promise<Record<UserRole, number>> {
  try {
    const params = {
      search: input.email,
      role: input.role.length > 0 ? (input.role[0] as UserRole) : undefined,
      status:
        input.status.length > 0 ? (input.status[0] as UserStatus) : undefined,
      limit: input.perPage,
      page: input.page,
      from: input.from || undefined,
      to: input.to || undefined,
    };
    const response = await getUserRoleCountsApi(params);
    if (response.status === ApiStatus.SUCCESS && response.data) {
      return response.data.counts;
    }
    return { admin: 0, staff: 0, user: 0, instructor: 0 };
  } catch {
    return { admin: 0, staff: 0, user: 0, instructor: 0 };
  }
}
