"use server";

import { revalidateTag, unstable_noStore } from "next/cache";

import { UserR<PERSON>, UserStatus, type User } from "@/types/user";
import { getErrorMessage } from "@/lib/data-table/handle-error";
import {
  adminUpdateUser,
  deleteUser as deleteUser<PERSON><PERSON>,
  deleteManyUsers,
} from "@/lib/users";

import type { UpdateUserSchema } from "./validations";

export async function updateUser(input: UpdateUserSchema & { id: string }) {
  unstable_noStore();
  try {
    const response = await adminUpdateUser({
      userId: input.id,
      role: input.role as UserRole,
      status: input.status as UserStatus,
    });

    revalidateTag("users");
    revalidateTag("user-status-counts");
    revalidateTag("user-role-counts");

    return {
      data: response.data,
      error: null,
    };
  } catch (err) {
    return {
      data: null,
      error: getErrorMessage(err),
    };
  }
}

export async function updateUsers(input: {
  ids: string[];
  role?: User["role"];
  status?: User["status"];
}) {
  unstable_noStore();
  try {
    // Since there's no bulk update endpoint, we'll update users one by one
    const updatePromises = input.ids.map((id) =>
      adminUpdateUser({
        userId: id,
        role: input.role,
        status: input.status,
      })
    );

    const results = await Promise.all(updatePromises);

    revalidateTag("users");
    revalidateTag("user-status-counts");
    revalidateTag("user-role-counts");

    return {
      data: results.map((r) => r.data),
      error: null,
    };
  } catch (err) {
    return {
      data: null,
      error: getErrorMessage(err),
    };
  }
}

export async function deleteUser(input: { id: string }) {
  unstable_noStore();
  try {
    const response = await deleteUserApi(input.id);

    revalidateTag("users");
    revalidateTag("user-status-counts");
    revalidateTag("user-role-counts");

    return {
      data: response.data,
      error: null,
    };
  } catch (err) {
    return {
      data: null,
      error: getErrorMessage(err),
    };
  }
}

export async function deleteUsers(input: { ids: string[] }) {
  unstable_noStore();
  try {
    const response = await deleteManyUsers({ userIds: input.ids });

    revalidateTag("users");
    revalidateTag("user-status-counts");
    revalidateTag("user-role-counts");

    return {
      data: response.data,
      error: null,
    };
  } catch (err) {
    return {
      data: null,
      error: getErrorMessage(err),
    };
  }
}
