"use client";

import { useState } from "react";
import { ThumbnailImageUploader } from "@/components/uploader/thumbnail-image-uploader";
import { FileUploader } from "@/components/uploader/file-uploader";
import { VideoUploader } from "@/components/uploader/video-uploader";
import { UploadedFile } from "@/types/common";

const TestPage = () => {
  const [uploadedImage, setUploadedImage] = useState<UploadedFile | null>(null);
  const [uploadedFile, setUploadedFile] = useState<UploadedFile | null>(null);
  const [uploadedVideo, setUploadedVideo] = useState<UploadedFile | null>(null);

  const handleImageChange = (image: UploadedFile | null) => {
    setUploadedImage(image);
  };

  const handleFileChange = (file: UploadedFile | null) => {
    setUploadedFile(file);
  };

  const handleVideoChange = (video: UploadedFile | null) => {
    setUploadedVideo(video);
  };

  return (
    <div className="container mx-auto p-8">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div className="space-y-6">
          <div>
            <h2 className="text-2xl font-bold mb-6">Image Uploader</h2>
            <div className="flex items-center justify-center">
              <ThumbnailImageUploader
                uploadedImage={uploadedImage}
                onImageChange={handleImageChange}
              />
            </div>
            {uploadedImage && (
              <div className="mt-4 p-4 bg-muted rounded-lg">
                <h3 className="font-semibold mb-2">Image Details:</h3>
                <pre className="text-sm">
                  {JSON.stringify(uploadedImage, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>

        <div className="space-y-6">
          <div>
            <h2 className="text-2xl font-bold mb-6">File Uploader</h2>
            <FileUploader
              uploadedFile={uploadedFile}
              onFileChange={handleFileChange}
            />
            {uploadedFile && (
              <div className="mt-4 p-4 bg-muted rounded-lg">
                <h3 className="font-semibold mb-2">File Details:</h3>
                <pre className="text-sm">
                  {JSON.stringify(uploadedFile, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>

        <div className="space-y-6">
          <div>
            <h2 className="text-2xl font-bold mb-6">Video Uploader</h2>
            <VideoUploader
              uploadedVideo={uploadedVideo}
              onVideoChange={handleVideoChange}
            />
            {uploadedVideo && (
              <div className="mt-4 p-4 bg-muted rounded-lg">
                <h3 className="font-semibold mb-2">Video Details:</h3>
                <pre className="text-sm">
                  {JSON.stringify(uploadedVideo, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestPage;
