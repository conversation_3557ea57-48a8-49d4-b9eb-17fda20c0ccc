"use client";

import { useState } from "react";
import { Shell } from "@/components/shell";
import { ContentLayout } from "@/components/admin-panel/content-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  BookOpen,
  Clock,
  DollarSign,
  FileText,
  CheckCircle2,
  Video,
} from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { Course, LessonType, CourseApprovalStatus } from "@/types/course";
import { Badge } from "@/components/ui/badge";
import { ApprovalStatusBadge } from "@/components/ui/approval-status-badge";
import { ApprovalControls } from "@/components/ui/approval-controls";
import { useAuth } from "@/contexts/auth-contexts";
import { approveCourse } from "@/lib/courses";
import { Role } from "@/types/auth";
import { toast } from "sonner";

interface CoursePreviewPageClientProps {
  initialData: Course;
}

export default function CoursePreviewPageClient({
  initialData,
}: CoursePreviewPageClientProps) {
  const router = useRouter();
  const { user } = useAuth();
  const [course, setCourse] = useState<Course>(initialData);
  const [isApprovalLoading, setIsApprovalLoading] = useState(false);

  const isAdmin = user?.role === Role.Admin;

  const handleApprovalChange = async (
    status: CourseApprovalStatus | string,
    comment?: string
  ) => {
    const courseId = course._id || course.id;
    if (!courseId) return;

    setIsApprovalLoading(true);
    try {
      const response = await approveCourse(courseId, {
        status: status as CourseApprovalStatus,
        comment,
      });
      if (response.data) {
        setCourse(response.data);
        toast.success("Course approval status updated successfully");
      } else {
        toast.error("Failed to update course approval status");
      }
    } catch (error) {
      console.error("Error updating course approval:", error);
      toast.error("Failed to update course approval status");
    } finally {
      setIsApprovalLoading(false);
    }
  };

  return (
    <ContentLayout title="Course Preview">
      <Shell>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              className="gap-2"
              onClick={() => router.back()}
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
          </div>

          <div className="grid gap-6">
            {/* Course Header */}
            <Card>
              <CardContent className="p-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="relative aspect-video rounded-lg overflow-hidden">
                    {course.thumbnail && course.thumbnail.key && (
                      <Image
                        src={`${process.env.NEXT_PUBLIC_AWS_IMAGES_CLOUDFRONT_URL}/${course.thumbnail.key}`}
                        alt={course.title}
                        fill
                        className="object-cover"
                      />
                    )}
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <h1 className="text-2xl font-bold">{course.title}</h1>
                      <Badge variant="outline" className="gap-1">
                        <BookOpen className="h-3 w-3" />
                        Course
                      </Badge>
                    </div>
                    <p className="text-muted-foreground">
                      {course.shortDescription}
                    </p>
                    <div className="flex items-center gap-4 flex-wrap">
                      {course.isFree ? (
                        <span className="text-green-600 font-semibold">
                          Free
                        </span>
                      ) : (
                        <div className="flex items-center gap-2">
                          <DollarSign className="h-4 w-4" />
                          <span className="font-semibold">${course.price}</span>
                          {course.discountPrice && (
                            <span className="text-muted-foreground line-through">
                              ${course.discountPrice}
                            </span>
                          )}
                        </div>
                      )}
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        <span>
                          Created{" "}
                          {new Date(course.createdAt).toLocaleDateString()}
                        </span>
                      </div>
                      <Badge
                        variant={
                          course.status === "published"
                            ? "default"
                            : "secondary"
                        }
                      >
                        {course.status === "published" ? (
                          <CheckCircle2 className="h-3 w-3 mr-1" />
                        ) : null}
                        {course.status}
                      </Badge>
                      {course.approvalStatus && (
                        <ApprovalStatusBadge status={course.approvalStatus} />
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Course Details */}
            <Card>
              <CardHeader>
                <CardTitle>Course Details</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold mb-2">Description</h3>
                    <p className="text-muted-foreground whitespace-pre-wrap">
                      {course.description}
                    </p>
                  </div>
                  <Separator />
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h3 className="font-semibold mb-2">Category</h3>
                      <p className="text-muted-foreground">{course.category}</p>
                    </div>
                    <div>
                      <h3 className="font-semibold mb-2">Subcategory</h3>
                      <p className="text-muted-foreground">
                        {course.subcategory}
                      </p>
                    </div>
                  </div>
                  {course.whatYouWillLearn &&
                    course.whatYouWillLearn.length > 0 && (
                      <>
                        <Separator />
                        <div>
                          <h3 className="font-semibold mb-2">
                            What You'll Learn
                          </h3>
                          <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                            {course.whatYouWillLearn.map((item, index) => (
                              <li key={index}>{item}</li>
                            ))}
                          </ul>
                        </div>
                      </>
                    )}
                </div>
              </CardContent>
            </Card>

            {/* Course Content */}
            <Card>
              <CardHeader>
                <CardTitle>Course Content</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-6">
                  {course.sections.map((section, index) => (
                    <div key={section._id || index} className="space-y-4">
                      <h3 className="text-lg font-semibold">
                        Section {index + 1}: {section.title}
                      </h3>
                      {section.description && (
                        <p className="text-muted-foreground">
                          {section.description}
                        </p>
                      )}
                      <div className="space-y-2">
                        {section.lessons.map((lesson, lessonIndex) => (
                          <div
                            key={lesson._id || lessonIndex}
                            className="flex items-center gap-4 p-3 rounded-lg border"
                          >
                            {lesson.type === LessonType.VIDEO ? (
                              <Video className="h-4 w-4 text-muted-foreground" />
                            ) : (
                              <FileText className="h-4 w-4 text-muted-foreground" />
                            )}
                            <div className="flex-1">
                              <p className="font-medium">{lesson.title}</p>
                              {lesson.description && (
                                <p className="text-sm text-muted-foreground">
                                  {lesson.description}
                                </p>
                              )}
                            </div>
                            {lesson.duration && (
                              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                <Clock className="h-3 w-3" />
                                {lesson.duration} min
                              </div>
                            )}
                            {lesson.isFree && (
                              <Badge variant="secondary">Free</Badge>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Course Preview */}
            {course.preview && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Video className="h-5 w-5" />
                    Course Preview
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <Button
                    variant="outline"
                    className="w-full gap-2"
                    onClick={() =>
                      window.open(
                        `${process.env.NEXT_PUBLIC_AWS_FILES_CLOUDFRONT_URL}/${course.preview!.key}`,
                        "_blank"
                      )
                    }
                  >
                    <Video className="h-4 w-4" />
                    View Preview
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* Approval Details */}
            {course.approvalComment ||
            course.approvedBy ||
            course.approvalDate ? (
              <Card>
                <CardHeader>
                  <CardTitle>Approval Details</CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {course.approvalComment && (
                      <div>
                        <h3 className="font-semibold mb-2">Approval Comment</h3>
                        <p className="text-muted-foreground whitespace-pre-wrap">
                          {course.approvalComment}
                        </p>
                      </div>
                    )}
                    <div className="grid grid-cols-2 gap-4">
                      {course.approvedBy && (
                        <div>
                          <h3 className="font-semibold mb-2">Approved By</h3>
                          <p className="text-muted-foreground">
                            {course.approvedBy}
                          </p>
                        </div>
                      )}
                      {course.approvalDate && (
                        <div>
                          <h3 className="font-semibold mb-2">Approval Date</h3>
                          <p className="text-muted-foreground">
                            {new Date(course.approvalDate).toLocaleDateString()}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : null}

            {/* Admin Approval Controls */}
            {isAdmin && course.approvalStatus && (
              <ApprovalControls
                currentStatus={course.approvalStatus}
                onApprove={handleApprovalChange}
                isLoading={isApprovalLoading}
              />
            )}
          </div>
        </div>
      </Shell>
    </ContentLayout>
  );
}
