import { getCourse } from "@/lib/courses";
import { ApiStatus } from "@/types/common";
import { Metadata } from "next";
import { notFound } from "next/navigation";
import CoursePreviewPageClient from "./_components/course-preview-page-client";

interface PreviewCoursePageProps {
  params: Promise<{ id: string }>;
}

export async function generateMetadata({
  params,
}: PreviewCoursePageProps): Promise<Metadata> {
  const response = await getCourse((await params).id);

  if (response.status === ApiStatus.FAIL || !response.data) {
    return {
      title: "Course Not Found",
      description: "The requested course preview could not be found.",
    };
  }

  return {
    title: `Preview: ${response.data.title}`,
    description: `Preview the course content and details for: ${response.data.title}`,
  };
}

export default async function PreviewCoursePage({
  params,
}: PreviewCoursePageProps) {
  const response = await getCourse((await params).id);

  if (response.status === ApiStatus.FAIL || !response.data) {
    return notFound();
  }

  return <CoursePreviewPageClient initialData={response.data} />;
}
