"use server";

import { type Course } from "@/types/course";
import { type GetCoursesSchema } from "./validations";
import {
  getCourses as getCoursesApi,
  getCourseCategoryCounts as getCourseCategoryCountsApi,
} from "@/lib/courses";
import { ApiStatus } from "@/types/common";

export async function getCourses(input: GetCoursesSchema): Promise<{
  data: Course[];
  pageCount: number;
}> {
  try {
    const params = {
      search: input.title,
      category: input.category.length > 0 ? input.category[0] : undefined,
      limit: input.perPage || 10,
      page: input.page || 1,
      filters: input.filters,
      sort: input.sort,
      from: input.from || undefined,
      to: input.to || undefined,
    };

    const response = await getCoursesApi(params);

    if (response.status === ApiStatus.SUCCESS && response.data) {
      return {
        data: response.data.courses,
        pageCount: response.data.totalPages,
      };
    }

    return { data: [], pageCount: 0 };
  } catch {
    return { data: [], pageCount: 0 };
  }
}

export async function getCourseCategoryCounts(
  input: GetCoursesSchema
): Promise<Record<string, number>> {
  try {
    const params = {
      search: input.title,
      category: input.category.length > 0 ? input.category[0] : undefined,
      limit: input.perPage,
      page: input.page,
      from: input.from || undefined,
      to: input.to || undefined,
    };
    const response = await getCourseCategoryCountsApi(params);
    if (response.status === ApiStatus.SUCCESS && response.data) {
      return response.data.counts;
    }
    return {};
  } catch {
    return {};
  }
}
