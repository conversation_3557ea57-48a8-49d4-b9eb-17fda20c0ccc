"use server";

import { revalidateTag, unstable_noStore } from "next/cache";
import { getErrorMessage } from "@/lib/data-table/handle-error";
import {
  updateCourses as updateCoursesApi,
  deleteCourses as deleteCoursesApi,
} from "@/lib/courses";
import type { UpdateCourseSchema } from "./validations";

export async function updateCourse(input: UpdateCourseSchema & { id: string }) {
  unstable_noStore();
  try {
    const response = await updateCoursesApi([input.id], input);

    revalidateTag("courses");
    revalidateTag("course-category-counts");

    return {
      data: response.data?.[0],
      error: null,
    };
  } catch (err) {
    return {
      data: null,
      error: getErrorMessage(err),
    };
  }
}

export async function updateCourses(input: {
  ids: string[];
  category?: string;
  price?: number;
  discountPrice?: number;
}) {
  unstable_noStore();
  try {
    const response = await updateCoursesApi(input.ids, {
      category: input.category,
      price: input.price,
      discountPrice: input.discountPrice,
    });

    revalidateTag("courses");
    revalidateTag("course-category-counts");

    return {
      data: response.data,
      error: null,
    };
  } catch (err) {
    return {
      data: null,
      error: getErrorMessage(err),
    };
  }
}

export async function deleteCourse(input: { id: string }) {
  unstable_noStore();
  try {
    const response = await deleteCoursesApi([input.id]);

    revalidateTag("courses");
    revalidateTag("course-category-counts");

    return {
      data: response.data,
      error: null,
    };
  } catch (err) {
    return {
      data: null,
      error: getErrorMessage(err),
    };
  }
}

export async function deleteCourses(input: { ids: string[] }) {
  unstable_noStore();
  try {
    const response = await deleteCoursesApi(input.ids);

    revalidateTag("courses");
    revalidateTag("course-category-counts");

    return {
      data: response.data,
      error: null,
    };
  } catch (err) {
    return {
      data: null,
      error: getErrorMessage(err),
    };
  }
}
