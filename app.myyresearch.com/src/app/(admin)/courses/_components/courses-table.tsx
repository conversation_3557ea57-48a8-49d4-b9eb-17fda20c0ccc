"use client";

import * as React from "react";
import {
  Api<PERSON>tatus,
  type DataTableAdvancedFilterField,
  type DataTableFilterField,
  type DataTableRowAction,
} from "@/types";

import { type Course } from "@/types/course";
import { toSentenceCase } from "@/lib/data-table/utils";
import { useDataTable } from "@/hooks/data-table/use-data-table";
import { DataTable } from "@/components/data-table/data-table";
import { DataTableAdvancedToolbar } from "@/components/data-table/data-table-advanced-toolbar";
import { DataTableToolbar } from "@/components/data-table/data-table-toolbar";

import type { getCourses, getCourseCategoryCounts } from "../_lib/queries";
import { DeleteCoursesDialog } from "./delete-courses-dialog";
import { getColumns } from "./courses-table-columns";
import { CoursesTableFloatingBar } from "./courses-table-floating-bar";
import { CoursesTableToolbarActions } from "./courses-table-toolbar-actions";
import { useRouter } from "next/navigation";
import { softDeleteCourse } from "@/lib/courses";
import { toast } from "sonner";

interface CoursesTableProps {
  promises: Promise<
    [
      Awaited<ReturnType<typeof getCourses>>,
      Awaited<ReturnType<typeof getCourseCategoryCounts>>,
    ]
  >;
}

export function CoursesTable({ promises }: CoursesTableProps) {
  const router = useRouter();

  const [{ data, pageCount }, categoryCounts] = React.use(promises);

  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<Course> | null>(null);

  const handleDelete = React.useCallback(
    async (id: string) => {
      const response = await softDeleteCourse(id);
      if (response.status === ApiStatus.SUCCESS) {
        toast.success("Course deleted successfully");
        router.refresh();
      } else {
        toast.error(response.message || "Failed to delete course");
      }
    },
    [router]
  );

  const columns = React.useMemo(
    () => getColumns({ setRowAction, onDelete: handleDelete }),
    [setRowAction, handleDelete]
  );

  const filterFields: DataTableFilterField<Course>[] = [
    {
      id: "title",
      label: "Title",
      placeholder: "Filter titles...",
    },
    {
      id: "category",
      label: "Category",
      options: Object.entries(categoryCounts).map(([category, count]) => ({
        label: toSentenceCase(category),
        value: category,
        count,
      })),
    },
  ];

  const advancedFilterFields: DataTableAdvancedFilterField<Course>[] = [
    {
      id: "title",
      label: "Title",
      type: "text",
    },
    {
      id: "category",
      label: "Category",
      type: "multi-select",
      options: Object.entries(categoryCounts).map(([category, count]) => ({
        label: toSentenceCase(category),
        value: category,
        count,
      })),
    },
    {
      id: "createdAt",
      label: "Created at",
      type: "date",
    },
  ];

  const { table } = useDataTable({
    data,
    columns,
    pageCount,
    filterFields,
    enableAdvancedFilter: true,
    initialState: {
      sorting: [{ id: "createdAt", desc: true }],
      columnPinning: { right: ["actions"] },
    },
    getRowId: (originalRow) => originalRow.id,
    shallow: false,
    clearOnDefault: true,
  });

  return (
    <>
      <DataTable
        table={table}
        floatingBar={<CoursesTableFloatingBar table={table} />}
      >
        <DataTableAdvancedToolbar
          table={table}
          filterFields={advancedFilterFields}
          shallow={false}
        >
          <CoursesTableToolbarActions
            table={table}
            onRefresh={() => router.refresh()}
          />
        </DataTableAdvancedToolbar>
      </DataTable>
      <DeleteCoursesDialog
        open={rowAction?.type === "delete"}
        onOpenChange={() => setRowAction(null)}
        courses={rowAction?.row.original ? [rowAction?.row.original] : []}
        showTrigger={false}
        onSuccess={() => rowAction?.row.toggleSelected(false)}
      />
    </>
  );
}
