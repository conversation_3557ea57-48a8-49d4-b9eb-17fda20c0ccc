"use client";

import * as React from "react";
import { type DataTableRowAction } from "@/types";
import { type ColumnDef } from "@tanstack/react-table";
import { Ellipsis } from "lucide-react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { type Course, CreatorType, CourseApprovalStatus } from "@/types/course";
import { type UploadedFile } from "@/types/common";
import { formatDate } from "@/lib/data-table/utils";
import { getErrorMessage } from "@/lib/data-table/handle-error";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { ApprovalStatusBadge } from "@/components/ui/approval-status-badge";
import { approveCourse } from "@/lib/courses";
import Image from "next/image";

interface GetColumnsProps {
  setRowAction: React.Dispatch<
    React.SetStateAction<DataTableRowAction<Course> | null>
  >;
  onDelete: (id: string) => Promise<void>;
}

export function getColumns({
  setRowAction,
  onDelete,
}: GetColumnsProps): ColumnDef<Course>[] {
  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          className="translate-y-0.5"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="translate-y-0.5"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "title",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Course" />
      ),
      cell: ({ row }) => {
        const thumbnail = row.original;
        const isInstructor =
          row.original.creatorType === CreatorType.INSTRUCTOR;
        const approvalStatus = row.original.approvalStatus;

        return (
          <div className="flex items-center gap-3">
            <div className="relative w-32 min-w-32">
              <div className="aspect-[16/9] overflow-hidden rounded-md">
                <Image
                  // @ts-ignore
                  src={`${process.env.NEXT_PUBLIC_AWS_IMAGES_CLOUDFRONT_URL}/${thumbnail["thumbnail"]}`}
                  alt={row.original.title}
                  className="h-full w-full object-cover"
                  width={128}
                  height={72}
                />
              </div>
            </div>
            <div className="min-w-[200px] flex-1">
              <div className="truncate font-medium">
                {row.getValue("title")}
              </div>
              {isInstructor && approvalStatus && (
                <div className="mt-1">
                  <ApprovalStatusBadge status={approvalStatus} />
                </div>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "category",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Category" />
      ),
      cell: ({ row }) => (
        <Badge variant="outline" className="capitalize">
          {row.getValue("category")}
        </Badge>
      ),
      filterFn: (row, id, value) => {
        return Array.isArray(value) && value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "price",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Price" />
      ),
      cell: ({ row }) => (
        <div className="w-[8rem]">
          ${row.getValue<number>("price").toFixed(2)}
        </div>
      ),
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Created At" />
      ),
      cell: ({ row }) => formatDate(row.getValue("createdAt")),
    },
    {
      accessorKey: "publicId",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Public ID" />
      ),
      cell: ({ row }) => <div>{row.getValue("publicId")}</div>,
    },
    {
      accessorKey: "shortDescription",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Description" />
      ),
      cell: ({ row }) => (
        <div className="max-w-[200px] truncate">
          {row.getValue("shortDescription")}
        </div>
      ),
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Status" />
      ),
      cell: ({ row }) => (
        <Badge
          variant={
            row.getValue("status") === "published" ? "default" : "secondary"
          }
          className="capitalize"
        >
          {row.getValue("status")}
        </Badge>
      ),
      filterFn: (row, id, value) => {
        return Array.isArray(value) && value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "enrollmentCount",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Enrollments" />
      ),
      cell: ({ row }) => <div>{row.getValue("enrollmentCount")}</div>,
    },
    {
      accessorKey: "creatorType",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Creator Type" />
      ),
      cell: ({ row }) => (
        <Badge variant="outline" className="capitalize">
          {row.getValue("creatorType")}
        </Badge>
      ),
    },
    {
      accessorKey: "instructorUsername",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Instructor" />
      ),
      cell: ({ row }) => <div>{row.getValue("instructorUsername")}</div>,
    },
    {
      id: "actions",
      cell: function Cell({ row }) {
        const router = useRouter();
        const [showDeleteDialog, setShowDeleteDialog] = React.useState(false);
        const [isApprovalPending, startApprovalTransition] =
          React.useTransition();

        const isInstructor =
          row.original.creatorType === CreatorType.INSTRUCTOR;
        const courseId = row.original.id || row.original._id;

        const handleApprovalChange = (status: CourseApprovalStatus) => {
          if (!courseId) return;

          startApprovalTransition(() => {
            toast.promise(approveCourse(courseId, { status }), {
              loading: "Updating approval status...",
              success: "Approval status updated",
              error: (err) => getErrorMessage(err),
            });
          });
        };

        return (
          <>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  aria-label="Open menu"
                  variant="ghost"
                  className="flex size-8 p-0 data-[state=open]:bg-muted"
                >
                  <Ellipsis className="size-4" aria-hidden="true" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-40">
                <DropdownMenuItem
                  onSelect={() => {
                    router.push(`/courses/preview/${courseId}`);
                  }}
                >
                  Preview
                </DropdownMenuItem>
                <DropdownMenuItem
                  onSelect={() => {
                    router.push(`/courses/edit/${courseId}`);
                  }}
                >
                  Edit
                </DropdownMenuItem>
                <DropdownMenuSeparator />

                {/* Approval actions for instructor courses */}
                {isInstructor && (
                  <>
                    <DropdownMenuSub>
                      <DropdownMenuSubTrigger>
                        Approval Status
                      </DropdownMenuSubTrigger>
                      <DropdownMenuSubContent>
                        <DropdownMenuRadioGroup
                          value={row.original.approvalStatus}
                          onValueChange={(value) => {
                            handleApprovalChange(value as CourseApprovalStatus);
                          }}
                        >
                          {Object.values(CourseApprovalStatus).map((status) => (
                            <DropdownMenuRadioItem
                              key={status}
                              value={status}
                              className="capitalize"
                              disabled={isApprovalPending}
                            >
                              {status.toLowerCase()}
                            </DropdownMenuRadioItem>
                          ))}
                        </DropdownMenuRadioGroup>
                      </DropdownMenuSubContent>
                    </DropdownMenuSub>
                    <DropdownMenuSeparator />
                  </>
                )}

                <DropdownMenuItem
                  onSelect={() => setShowDeleteDialog(true)}
                  className="text-destructive focus:text-destructive"
                >
                  Delete
                  <DropdownMenuShortcut>⌘⌫</DropdownMenuShortcut>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Delete Course</DialogTitle>
                  <DialogDescription>
                    Are you sure you want to delete this course? This action
                    cannot be undone.
                  </DialogDescription>
                </DialogHeader>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setShowDeleteDialog(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={() => {
                      if (courseId) {
                        onDelete(courseId);
                        setShowDeleteDialog(false);
                      }
                    }}
                  >
                    Delete
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </>
        );
      },
    },
  ];
}
