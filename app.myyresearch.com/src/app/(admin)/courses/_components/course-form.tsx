"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Shell } from "@/components/shell";
import { ContentLayout } from "@/components/admin-panel/content-layout";
import { CourseInformationForm } from "../add/_components/course-information-form";
import { CourseContentForm } from "../add/_components/course-content-form";
import { ApiStatus } from "@/types";
import { CreateCourseDto, CreateSectionDto } from "@/types/course";
import { UploadedFile } from "@/types/common";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { Separator } from "@/components/ui/separator";
import { Card, CardContent } from "@/components/ui/card";
import { SaveIcon, SendIcon, Loader2 } from "lucide-react";
import { createCourse, updateCourseWithAuth } from "@/lib/courses";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

const formSchema = z.object({
  title: z.string().min(1, "Title is required"),
  shortDescription: z.string().min(1, "Short description is required"),
  description: z.string().optional().default(""),
  category: z.string().min(1, "Category is required"),
  subcategory: z.string().min(1, "Subcategory is required"),
  isFree: z.boolean().default(false),
  price: z.number().min(0, "Price must be greater than or equal to 0"),
  discountPrice: z.number().min(0).optional(),
  thumbnail: z.custom<UploadedFile | null>(),
  preview: z.custom<UploadedFile | null>().optional(),
  whatYouWillLearn: z
    .array(z.string())
    .min(1, "What you will learn is required"),
});

interface CourseFormProps {
  initialData?: CreateCourseDto;
  mode: "add" | "edit";
  courseId?: string;
}

export function CourseForm({ initialData, mode, courseId }: CourseFormProps) {
  const [sections, setSections] = useState<CreateSectionDto[]>(
    initialData?.sections || []
  );
  const [thumbnail, setThumbnail] = useState<UploadedFile | null>(
    initialData?.thumbnail || null
  );
  const [preview, setPreview] = useState<UploadedFile | null>(
    initialData?.preview || null
  );
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPublishDialog, setShowPublishDialog] = useState(false);
  const { toast } = useToast();
  const router = useRouter();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: initialData?.title || "",
      shortDescription: initialData?.shortDescription || "",
      description: initialData?.description || "",
      category: initialData?.category || "",
      subcategory: initialData?.subcategory || "",
      isFree: initialData?.isFree || false,
      price: initialData?.price || 0,
      discountPrice: initialData?.discountPrice,
      thumbnail: initialData?.thumbnail || null,
      preview: initialData?.preview || null,
      whatYouWillLearn: initialData?.whatYouWillLearn || [],
    },
  });

  const handleSubmit = async (
    values: z.infer<typeof formSchema>,
    isDraft: boolean = false
  ) => {
    if (!thumbnail) {
      toast({
        title: "Thumbnail is required",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const courseData: CreateCourseDto = {
        ...values,
        thumbnail,
        preview: preview || undefined,
        sections,
        status: isDraft ? "draft" : "published",
        whatYouWillLearn: values.whatYouWillLearn,
      };

      let res;
      if (mode === "add") {
        res = await createCourse(courseData);
      } else if (mode === "edit" && courseId) {
        res = await updateCourseWithAuth(courseId, courseData);
      }

      if (res?.status === ApiStatus.SUCCESS) {
        toast({
          title: `Course ${mode === "add" ? "created" : "updated"} successfully`,
        });
        router.push("/courses");
      }
    } catch (error) {
      console.error(error);
      toast({
        title: "Something went wrong",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const onSaveAsDraft = async (values: z.infer<typeof formSchema>) => {
    await handleSubmit(values, true);
  };

  const onPublish = async (values: z.infer<typeof formSchema>) => {
    setShowPublishDialog(false);
    await handleSubmit(values, false);
  };

  return (
    <ContentLayout title={mode === "add" ? "Add New Course" : "Edit Course"}>
      <Shell>
        <div className="space-y-8 pb-10">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-semibold tracking-tight">
                  {mode === "add" ? "Create New Course" : "Edit Course"}
                </h1>
                <p className="text-sm text-muted-foreground">
                  Fill in the course details and content structure
                </p>
              </div>
              <div className="flex items-center gap-4">
                <Button
                  type="button"
                  variant="outline"
                  size="lg"
                  className="gap-2"
                  onClick={() => form.handleSubmit(onSaveAsDraft)()}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span>Saving...</span>
                    </>
                  ) : (
                    <>
                      <SaveIcon className="h-4 w-4" />
                      <span>Save as Draft</span>
                    </>
                  )}
                </Button>
                <Dialog
                  open={showPublishDialog}
                  onOpenChange={setShowPublishDialog}
                >
                  <DialogTrigger asChild>
                    <Button
                      type="button"
                      size="lg"
                      className="gap-2"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          <span>Publishing...</span>
                        </>
                      ) : (
                        <>
                          <SendIcon className="h-4 w-4" />
                          <span>Publish Course</span>
                        </>
                      )}
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Publish Course</DialogTitle>
                      <DialogDescription>
                        Are you sure you want to publish this course? This will
                        make the course visible to all students.
                      </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                      <Button
                        variant="outline"
                        onClick={() => setShowPublishDialog(false)}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={() => form.handleSubmit(onPublish)()}
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? (
                          <>
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            Publishing...
                          </>
                        ) : (
                          "Confirm Publish"
                        )}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </div>

            <Separator />

            <div className="grid gap-6">
              <Card>
                <CardContent className="p-6">
                  <Form {...form}>
                    <CourseInformationForm
                      onSubmit={onPublish}
                      onSaveAsDraft={onSaveAsDraft}
                      thumbnail={thumbnail}
                      setThumbnail={setThumbnail}
                      preview={preview}
                      setPreview={setPreview}
                      isSubmitting={isSubmitting}
                      form={form}
                    />
                  </Form>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <CourseContentForm
                    sections={sections}
                    setSections={setSections}
                  />
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </Shell>
    </ContentLayout>
  );
}
