"use client";

import { type Table } from "@tanstack/react-table";
import { Download, Plus } from "lucide-react";
import Link from "next/link";

import { type Course } from "@/types/course";
import { exportTableToCSV } from "@/lib/data-table/export";
import { Button } from "@/components/ui/button";

import { DeleteCoursesDialog } from "./delete-courses-dialog";

interface CoursesTableToolbarActionsProps {
  table: Table<Course>;
  onRefresh?: () => void;
}

export function CoursesTableToolbarActions({
  table,
  onRefresh,
}: CoursesTableToolbarActionsProps) {
  return (
    <div className="flex items-center gap-2">
      {table.getFilteredSelectedRowModel().rows.length > 0 ? (
        <DeleteCoursesDialog
          courses={table
            .getFilteredSelectedRowModel()
            .rows.map((row) => row.original)}
          onSuccess={() => {
            table.toggleAllRowsSelected(false);
            onRefresh?.();
          }}
        />
      ) : null}
      <Button variant="default" size="sm" className="gap-2" asChild>
        <Link href="/courses/add">
          <Plus className="size-4" aria-hidden="true" />
          Add Course
        </Link>
      </Button>
      <Button
        variant="outline"
        size="sm"
        onClick={() =>
          exportTableToCSV(table, {
            filename: "courses",
            excludeColumns: ["select", "actions"],
          })
        }
        className="gap-2"
      >
        <Download className="size-4" aria-hidden="true" />
        Export
      </Button>
    </div>
  );
}
