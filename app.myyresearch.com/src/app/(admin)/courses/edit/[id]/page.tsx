import { getCourse } from "@/lib/courses";
import { CourseForm } from "../../_components/course-form";
import { notFound } from "next/navigation";
import { ApiStatus } from "@/types";
import { Metadata } from "next";

type Props = {
  params: Promise<{ id: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const response = await getCourse((await params).id);

  if (response.status === ApiStatus.FAIL || !response.data) {
    return {
      title: "Course Not Found",
      description: "The requested course could not be found.",
    };
  }

  return {
    title: `Edit ${response.data.title}`,
    description: `Edit and update details for the course: ${response.data.title}`,
  };
}

const EditCoursePage = async ({ params }: Props) => {
  const response = await getCourse((await params).id);

  if (response.status === ApiStatus.FAIL || !response.data) {
    return notFound();
  }

  return (
    <CourseForm
      mode="edit"
      courseId={(await params).id}
      initialData={response.data}
    />
  );
};

export default EditCoursePage;
