import * as React from "react";
import { type SearchParams } from "@/types";
import { ContentLayout } from "@/components/admin-panel/content-layout";
import Link from "next/link";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

import { Skeleton } from "@/components/ui/skeleton";
import { DataTableSkeleton } from "@/components/data-table/data-table-skeleton";
import { DateRangePicker } from "@/components/date-range-picker";
import { Shell } from "@/components/shell";

import { CoursesTable } from "./_components/courses-table";
import { getCourses, getCourseCategoryCounts } from "./_lib/queries";
import { searchParamsCache } from "./_lib/validations";
import { getValidFilters } from "@/lib/data-table/data-table";
import { type Metadata } from "next";

export const metadata: Metadata = {
  title: "Courses",
  description: "Manage your courses",
};

interface IndexPageProps {
  searchParams: Promise<SearchParams>;
}

export default async function IndexPage(props: IndexPageProps) {
  const searchParams = await props.searchParams;
  const search = searchParamsCache.parse(searchParams);
  const validFilters = getValidFilters(search.filters);
  const promises = Promise.all([
    getCourses({
      ...search,
      filters: validFilters,
    }),
    getCourseCategoryCounts({
      ...search,
      filters: validFilters,
    }),
  ]);

  return (
    <ContentLayout title="Courses">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">Home</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Courses</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <Shell className="gap-2">
        <React.Suspense
          fallback={
            <DataTableSkeleton
              columnCount={6}
              searchableColumnCount={1}
              filterableColumnCount={1}
              cellWidths={["10rem", "15rem", "12rem", "8rem", "12rem", "8rem"]}
              shrinkZero
            />
          }
        >
          <CoursesTable promises={promises} />
        </React.Suspense>
      </Shell>
    </ContentLayout>
  );
}
