import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import {
  Plus,
  Trash2,
  GripVertical,
  BookOpen,
  Video,
  FileText,
  LayoutList,
  PlusCircle,
  Settings2,
  ArrowUp,
  ArrowDown,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogTrigger,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { CreateSectionDto, CreateLessonDto, LessonType } from "@/types/course";
import { UploadedFile } from "@/types/common";
import { VideoUploader } from "@/components/uploader/video-uploader";
import TiptapEditor from "@/components/editor/editor";
import {
  DragDropContext,
  Droppable,
  Draggable,
  DropResult,
  DroppableProvided,
  DraggableProvided,
  DraggableStateSnapshot,
} from "@hello-pangea/dnd";
import { FileUploader } from "@/components/uploader/file-uploader";

interface ExtendedCreateLessonDto extends Omit<CreateLessonDto, "video"> {
  content: string;
  video?: UploadedFile;
  media?: UploadedFile;
  type: LessonType;
}

interface ExtendedCreateSectionDto extends Omit<CreateSectionDto, "lessons"> {
  lessons: ExtendedCreateLessonDto[];
}

const sectionFormSchema = z.object({
  title: z.string().min(1, "Section title is required"),
});

const lessonFormSchema = z
  .object({
    title: z.string().min(1, "Lesson title is required"),
    type: z.nativeEnum(LessonType).default(LessonType.VIDEO),
    isFree: z.boolean().default(false),
    duration: z
      .number()
      .min(1, "Duration is required for video lessons")
      .optional(),
  })
  .refine(
    (data) => {
      // If type is VIDEO, duration is required
      if (data.type === LessonType.VIDEO) {
        return data.duration !== undefined && data.duration > 0;
      }
      return true;
    },
    {
      message: "Duration is required for video lessons",
      path: ["duration"],
    }
  );

interface CourseContentFormProps {
  sections: CreateSectionDto[];
  setSections: React.Dispatch<React.SetStateAction<CreateSectionDto[]>>;
}

export function CourseContentForm({
  sections,
  setSections,
}: CourseContentFormProps) {
  const [expandedSection, setExpandedSection] = useState<string | undefined>(
    undefined
  );
  const [expandedLesson, setExpandedLesson] = useState<string | undefined>(
    undefined
  );
  const [isAddSectionOpen, setIsAddSectionOpen] = useState(false);
  const [isAddLessonOpen, setIsAddLessonOpen] = useState(false);
  const [isEditLessonOpen, setIsEditLessonOpen] = useState(false);
  const [isEditSectionOpen, setIsEditSectionOpen] = useState(false);
  const [activeSectionIndex, setActiveSectionIndex] = useState<number | null>(
    null
  );
  const [activeLessonIndex, setActiveLessonIndex] = useState<number | null>(
    null
  );
  const [isDeleteSectionOpen, setIsDeleteSectionOpen] = useState(false);
  const [isDeleteLessonOpen, setIsDeleteLessonOpen] = useState(false);
  const [deletingSectionIndex, setDeletingSectionIndex] = useState<
    number | null
  >(null);
  const [deletingLessonIndices, setDeletingLessonIndices] = useState<{
    sectionIndex: number;
    lessonIndex: number;
  } | null>(null);

  const sectionForm = useForm<z.infer<typeof sectionFormSchema>>({
    resolver: zodResolver(sectionFormSchema),
    defaultValues: {
      title: "",
    },
  });

  const lessonForm = useForm<z.infer<typeof lessonFormSchema>>({
    resolver: zodResolver(lessonFormSchema),
    defaultValues: {
      title: "",
      type: LessonType.VIDEO,
      isFree: false,
    },
  });

  const editLessonForm = useForm<z.infer<typeof lessonFormSchema>>({
    resolver: zodResolver(lessonFormSchema),
    defaultValues: {
      title: "",
      type: LessonType.VIDEO,
      isFree: false,
    },
  });

  const editSectionForm = useForm<z.infer<typeof sectionFormSchema>>({
    resolver: zodResolver(sectionFormSchema),
    defaultValues: {
      title: "",
    },
  });

  const onAddSection = (values: z.infer<typeof sectionFormSchema>) => {
    const newSection: ExtendedCreateSectionDto = {
      title: values.title,
      order: sections.length,
      lessons: [],
    };
    setSections([...sections, newSection]);
    sectionForm.reset();
    setIsAddSectionOpen(false);

    setTimeout(() => {
      const newSectionElement = document.getElementById(
        `section-${sections.length}`
      );
      newSectionElement?.scrollIntoView({ behavior: "smooth" });
    }, 100);
  };

  const removeSection = (index: number) => {
    setDeletingSectionIndex(index);
    setIsDeleteSectionOpen(true);
  };

  const confirmRemoveSection = () => {
    if (deletingSectionIndex !== null) {
      setSections(sections.filter((_, i) => i !== deletingSectionIndex));
      setIsDeleteSectionOpen(false);
      setDeletingSectionIndex(null);
    }
  };

  const addLesson = (sectionIndex: number, e?: React.MouseEvent) => {
    e?.preventDefault();
    setActiveSectionIndex(sectionIndex);
    setIsAddLessonOpen(true);
  };

  const onAddLesson = (values: z.infer<typeof lessonFormSchema>) => {
    if (activeSectionIndex === null) return;

    const updatedSections = [...sections];
    const section = updatedSections[activeSectionIndex];

    if (section) {
      const newLessonIndex = section.lessons?.length || 0;
      const newLesson: ExtendedCreateLessonDto = {
        title: values.title,
        type: values.type,
        order: newLessonIndex,
        isFree: values.isFree,
        content: "",
      };

      section.lessons = [...(section.lessons || []), newLesson];
      setSections(updatedSections);
      setExpandedSection(`section-${activeSectionIndex}`);
      setExpandedLesson(`lesson-${activeSectionIndex}-${newLessonIndex}`);

      setTimeout(() => {
        const newLessonElement = document.getElementById(
          `lesson-${activeSectionIndex}-${newLessonIndex}`
        );
        newLessonElement?.scrollIntoView({ behavior: "smooth" });
      }, 100);
    }

    lessonForm.reset();
    setIsAddLessonOpen(false);
    setActiveSectionIndex(null);
  };

  const removeLesson = (sectionIndex: number, lessonIndex: number) => {
    setDeletingLessonIndices({ sectionIndex, lessonIndex });
    setIsDeleteLessonOpen(true);
  };

  const confirmRemoveLesson = () => {
    if (deletingLessonIndices) {
      const { sectionIndex, lessonIndex } = deletingLessonIndices;
      const updatedSections = [...sections];
      const section = updatedSections[sectionIndex];

      if (section && section.lessons) {
        section.lessons = section.lessons.filter((_, i) => i !== lessonIndex);
        setSections(updatedSections);
      }
      setIsDeleteLessonOpen(false);
      setDeletingLessonIndices(null);
    }
  };

  const updateLesson = (
    sectionIndex: number,
    lessonIndex: number,
    field: string,
    value: any
  ) => {
    const updatedSections = [...sections];
    const section = updatedSections[sectionIndex];

    if (section && section.lessons && section.lessons[lessonIndex]) {
      section.lessons[lessonIndex] = {
        ...section.lessons[lessonIndex],
        [field]: value,
      };
      setSections(updatedSections);
    }
  };

  const editLesson = (sectionIndex: number, lessonIndex: number) => {
    const section = sections[sectionIndex];
    const lesson = section.lessons?.[lessonIndex];

    if (lesson) {
      editLessonForm.reset({
        title: lesson.title,
        type: lesson.type,
        isFree: lesson.isFree,
        duration: lesson.duration || undefined,
      });
      setActiveSectionIndex(sectionIndex);
      setActiveLessonIndex(lessonIndex);
      setIsEditLessonOpen(true);
    }
  };

  const onEditLesson = (values: z.infer<typeof lessonFormSchema>) => {
    if (activeSectionIndex === null || activeLessonIndex === null) return;

    const updatedSections = [...sections];
    const section = updatedSections[activeSectionIndex];

    if (section && section.lessons && section.lessons[activeLessonIndex]) {
      section.lessons[activeLessonIndex] = {
        ...section.lessons[activeLessonIndex],
        ...values,
        duration:
          values.type === LessonType.VIDEO ? values.duration : undefined,
      };
      setSections(updatedSections);
    }

    editLessonForm.reset();
    setIsEditLessonOpen(false);
    setActiveSectionIndex(null);
    setActiveLessonIndex(null);
  };

  const editSection = (sectionIndex: number) => {
    const section = sections[sectionIndex];

    if (section) {
      editSectionForm.reset({
        title: section.title,
      });
      setActiveSectionIndex(sectionIndex);
      setIsEditSectionOpen(true);
    }
  };

  const onEditSection = (values: z.infer<typeof sectionFormSchema>) => {
    if (activeSectionIndex === null) return;

    const updatedSections = [...sections];
    const section = updatedSections[activeSectionIndex];

    if (section) {
      section.title = values.title;
      setSections(updatedSections);
    }

    editSectionForm.reset();
    setIsEditSectionOpen(false);
    setActiveSectionIndex(null);
  };

  const moveLesson = (
    sectionIndex: number,
    lessonIndex: number,
    direction: "up" | "down"
  ) => {
    const updatedSections = [...sections];
    const section = updatedSections[sectionIndex];

    if (!section || !section.lessons) return;

    const newIndex = direction === "up" ? lessonIndex - 1 : lessonIndex + 1;

    if (newIndex < 0 || newIndex >= section.lessons.length) return;

    // Swap lessons
    const temp = section.lessons[lessonIndex];
    section.lessons[lessonIndex] = section.lessons[newIndex];
    section.lessons[newIndex] = temp;

    // Update order
    section.lessons.forEach((lesson, index) => {
      lesson.order = index;
    });

    setSections(updatedSections);
  };

  const onDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const { source, destination } = result;
    const [sourceType] = source.droppableId.split("-");

    if (sourceType === "sections") {
      // Reorder sections
      const updatedSections = [...sections];
      const [removed] = updatedSections.splice(source.index, 1);
      updatedSections.splice(destination.index, 0, removed);

      // Update order
      updatedSections.forEach((section, index) => {
        section.order = index;
      });

      setSections(updatedSections);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-semibold">Course Content</h2>
        <p className="text-sm text-muted-foreground">
          Organize your course content into sections and lessons
        </p>
      </div>

      <DragDropContext onDragEnd={onDragEnd}>
        <Droppable droppableId="sections">
          {(provided) => (
            <div
              {...provided.droppableProps}
              ref={provided.innerRef}
              className="space-y-4"
            >
              {sections.map((section, sectionIndex) => (
                <Draggable
                  key={`section-${sectionIndex}`}
                  draggableId={`section-${sectionIndex}`}
                  index={sectionIndex}
                >
                  {(provided, snapshot) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      className={`rounded-lg border ${
                        snapshot.isDragging ? "border-primary" : ""
                      }`}
                      id={`section-${sectionIndex}`}
                    >
                      <div className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <div
                              {...provided.dragHandleProps}
                              className="cursor-move"
                            >
                              <GripVertical className="h-5 w-5 text-muted-foreground" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold">
                                {section.title}
                              </h3>
                              <p className="text-sm text-muted-foreground">
                                {section.lessons?.length || 0} lessons
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => editSection(sectionIndex)}
                            >
                              <Settings2 className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeSection(sectionIndex)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>

                        <div className="mt-4 space-y-2">
                          <Accordion
                            type="single"
                            collapsible
                            value={expandedLesson}
                            onValueChange={setExpandedLesson}
                          >
                            {section.lessons?.map(
                              (currentLesson, lessonIndex) => (
                                <AccordionItem
                                  key={`lesson-${sectionIndex}-${lessonIndex}`}
                                  value={`lesson-${sectionIndex}-${lessonIndex}`}
                                  className="rounded-lg border bg-muted/50 mb-3"
                                  id={`lesson-${sectionIndex}-${lessonIndex}`}
                                >
                                  <div className="p-3">
                                    <div className="flex items-center justify-between">
                                      <div>
                                        <div className="flex items-center gap-2">
                                          <h4 className="font-medium">
                                            {currentLesson.title}
                                          </h4>
                                          {currentLesson.isFree && (
                                            <Badge variant="secondary">
                                              Free
                                            </Badge>
                                          )}
                                          <Badge>
                                            {currentLesson.type ===
                                            LessonType.VIDEO
                                              ? "Video"
                                              : "Text"}
                                          </Badge>
                                        </div>
                                      </div>
                                      <div className="flex items-center gap-2">
                                        <div className="flex items-center">
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() =>
                                              moveLesson(
                                                sectionIndex,
                                                lessonIndex,
                                                "up"
                                              )
                                            }
                                            disabled={lessonIndex === 0}
                                          >
                                            <ArrowUp className="h-4 w-4" />
                                          </Button>
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() =>
                                              moveLesson(
                                                sectionIndex,
                                                lessonIndex,
                                                "down"
                                              )
                                            }
                                            disabled={
                                              lessonIndex ===
                                              (section.lessons?.length || 0) - 1
                                            }
                                          >
                                            <ArrowDown className="h-4 w-4" />
                                          </Button>
                                        </div>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() =>
                                            editLesson(
                                              sectionIndex,
                                              lessonIndex
                                            )
                                          }
                                        >
                                          <Settings2 className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() =>
                                            removeLesson(
                                              sectionIndex,
                                              lessonIndex
                                            )
                                          }
                                        >
                                          <Trash2 className="h-4 w-4" />
                                        </Button>
                                        <AccordionTrigger className="hover:no-underline py-0 px-2">
                                          <span className="sr-only">
                                            Toggle lesson content
                                          </span>
                                        </AccordionTrigger>
                                      </div>
                                    </div>

                                    <AccordionContent>
                                      {currentLesson.type ===
                                      LessonType.VIDEO ? (
                                        <div className="space-y-4 mt-4">
                                          <div>
                                            <h4 className="text-sm font-medium mb-2">
                                              Video Content
                                            </h4>
                                            <VideoUploader
                                              uploadedVideo={
                                                currentLesson.video || null
                                              }
                                              onVideoChange={(video) =>
                                                updateLesson(
                                                  sectionIndex,
                                                  lessonIndex,
                                                  "video",
                                                  video
                                                )
                                              }
                                            />
                                          </div>

                                          <div>
                                            <h4 className="text-sm font-medium mb-2">
                                              Additional Content
                                            </h4>
                                            <TiptapEditor
                                              content={
                                                currentLesson.content || ""
                                              }
                                              aiContent={null}
                                              onContentChange={(content) =>
                                                updateLesson(
                                                  sectionIndex,
                                                  lessonIndex,
                                                  "content",
                                                  content
                                                )
                                              }
                                            />
                                          </div>
                                          <div>
                                            <h4 className="text-sm font-medium mb-2">
                                              Media File
                                            </h4>
                                            <FileUploader
                                              uploadedFile={
                                                currentLesson.media || null
                                              }
                                              onFileChange={(file) =>
                                                updateLesson(
                                                  sectionIndex,
                                                  lessonIndex,
                                                  "media",
                                                  file
                                                )
                                              }
                                            />
                                          </div>
                                        </div>
                                      ) : (
                                        <div className="space-y-4 mt-4">
                                          <div>
                                            <h4 className="text-sm font-medium mb-2">
                                              Lesson Content
                                            </h4>
                                            <TiptapEditor
                                              content={
                                                currentLesson.content || ""
                                              }
                                              aiContent={null}
                                              onContentChange={(content) =>
                                                updateLesson(
                                                  sectionIndex,
                                                  lessonIndex,
                                                  "content",
                                                  content
                                                )
                                              }
                                            />
                                          </div>
                                          <div>
                                            <h4 className="text-sm font-medium mb-2">
                                              Media File
                                            </h4>
                                            <FileUploader
                                              uploadedFile={
                                                currentLesson.media || null
                                              }
                                              onFileChange={(file) =>
                                                updateLesson(
                                                  sectionIndex,
                                                  lessonIndex,
                                                  "media",
                                                  file
                                                )
                                              }
                                            />
                                          </div>
                                        </div>
                                      )}
                                    </AccordionContent>
                                  </div>
                                </AccordionItem>
                              )
                            )}
                          </Accordion>
                          <Button
                            variant="ghost"
                            className="w-full justify-start gap-2"
                            onClick={(e) => addLesson(sectionIndex, e)}
                          >
                            <PlusCircle className="h-4 w-4" />
                            Add Lesson
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>

      <Button
        variant="outline"
        className="w-full gap-2"
        onClick={() => setIsAddSectionOpen(true)}
      >
        <Plus className="h-4 w-4" />
        Add Section
      </Button>

      {/* Add Section Dialog */}
      <Dialog open={isAddSectionOpen} onOpenChange={setIsAddSectionOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Section</DialogTitle>
            <DialogDescription>
              Create a new section to organize your course content
            </DialogDescription>
          </DialogHeader>

          <Form {...sectionForm}>
            <form
              onSubmit={sectionForm.handleSubmit(onAddSection)}
              className="space-y-4"
            >
              <FormField
                control={sectionForm.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Section Title</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormDescription>
                      Give your section a clear and descriptive title
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsAddSectionOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">Add Section</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Add Lesson Dialog */}
      <Dialog open={isAddLessonOpen} onOpenChange={setIsAddLessonOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Lesson</DialogTitle>
            <DialogDescription>
              Create a new lesson for your course section
            </DialogDescription>
          </DialogHeader>

          <Form {...lessonForm}>
            <form
              onSubmit={lessonForm.handleSubmit(onAddLesson)}
              className="space-y-4"
            >
              <FormField
                control={lessonForm.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Lesson Title</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormDescription>
                      Give your lesson a clear and descriptive title
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={lessonForm.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Lesson Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select lesson type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value={LessonType.VIDEO}>
                          <div className="flex items-center gap-2">
                            <Video className="h-4 w-4" />
                            <span>Video Lesson</span>
                          </div>
                        </SelectItem>
                        <SelectItem value={LessonType.TEXT}>
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4" />
                            <span>Text Lesson</span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Choose the type of content for this lesson
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {lessonForm.watch("type") === LessonType.VIDEO && (
                <FormField
                  control={lessonForm.control}
                  name="duration"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Duration (minutes) *</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          required
                          min="1"
                          {...field}
                          onChange={(e) =>
                            field.onChange(Number(e.target.value))
                          }
                        />
                      </FormControl>
                      <FormDescription>
                        Enter the duration of the video in minutes (required)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              <FormField
                control={lessonForm.control}
                name="isFree"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Free Lesson</FormLabel>
                      <FormDescription>
                        Make this lesson available for free preview
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsAddLessonOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">Add Lesson</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Edit Lesson Dialog */}
      <Dialog open={isEditLessonOpen} onOpenChange={setIsEditLessonOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Lesson</DialogTitle>
            <DialogDescription>
              Update the lesson details and content
            </DialogDescription>
          </DialogHeader>

          <Form {...editLessonForm}>
            <form
              onSubmit={editLessonForm.handleSubmit(onEditLesson)}
              className="space-y-4"
            >
              <FormField
                control={editLessonForm.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Lesson Title</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormDescription>
                      Give your lesson a clear and descriptive title
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editLessonForm.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Lesson Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select lesson type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value={LessonType.VIDEO}>
                          <div className="flex items-center gap-2">
                            <Video className="h-4 w-4" />
                            <span>Video Lesson</span>
                          </div>
                        </SelectItem>
                        <SelectItem value={LessonType.TEXT}>
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4" />
                            <span>Text Lesson</span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Choose the type of content for this lesson
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {editLessonForm.watch("type") === LessonType.VIDEO && (
                <FormField
                  control={editLessonForm.control}
                  name="duration"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Duration (minutes) *</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          required
                          min="1"
                          {...field}
                          onChange={(e) =>
                            field.onChange(Number(e.target.value))
                          }
                          value={field.value || ""}
                        />
                      </FormControl>
                      <FormDescription>
                        Enter the duration of the video in minutes (required)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              <FormField
                control={editLessonForm.control}
                name="isFree"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Free Lesson</FormLabel>
                      <FormDescription>
                        Make this lesson available for free preview
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEditLessonOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">Save Changes</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Edit Section Dialog */}
      <Dialog open={isEditSectionOpen} onOpenChange={setIsEditSectionOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Section</DialogTitle>
            <DialogDescription>Update the section details</DialogDescription>
          </DialogHeader>

          <Form {...editSectionForm}>
            <form
              onSubmit={editSectionForm.handleSubmit(onEditSection)}
              className="space-y-4"
            >
              <FormField
                control={editSectionForm.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Section Title</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormDescription>
                      Give your section a clear and descriptive title
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEditSectionOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">Save Changes</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Section Dialog */}
      <Dialog open={isDeleteSectionOpen} onOpenChange={setIsDeleteSectionOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Section</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this section? This action cannot
              be undone. All lessons within this section will also be deleted.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteSectionOpen(false)}
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={confirmRemoveSection}
            >
              Delete Section
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Lesson Dialog */}
      <Dialog open={isDeleteLessonOpen} onOpenChange={setIsDeleteLessonOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Lesson</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this lesson? This action cannot be
              undone. All content within this lesson will be permanently
              deleted.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteLessonOpen(false)}
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={confirmRemoveLesson}
            >
              Delete Lesson
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
