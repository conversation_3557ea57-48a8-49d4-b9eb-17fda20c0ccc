import { UseFormReturn } from "react-hook-form";
import * as z from "zod";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import TiptapEditor from "@/components/editor/editor";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";

import { ThumbnailImageUploader } from "@/components/uploader/thumbnail-image-uploader";
import { UploadedFile } from "@/types/common";
import { courseCategories } from "@/data";
import {
  BookOpenIcon,
  ImageIcon,
  BookmarkIcon,
  FolderIcon,
  DollarSignIcon,
  AlertCircle,
} from "lucide-react";
import {
  Accordion,
  <PERSON>ccordion<PERSON>ontent,
  AccordionI<PERSON>,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { VideoUploader } from "@/components/uploader/video-uploader";
import { PlayCircle } from "lucide-react";
import { cn } from "@/lib/utils";

const formSchema = z.object({
  title: z.string().min(1, "Title is required"),
  shortDescription: z.string().min(1, "Short description is required"),
  description: z.string().optional().default(""),
  category: z.string().min(1, "Category is required"),
  subcategory: z.string().min(1, "Subcategory is required"),
  isFree: z.boolean().default(false),
  price: z.number().min(0, "Price must be greater than or equal to 0"),
  discountPrice: z.number().min(0).optional(),
  thumbnail: z.custom<UploadedFile | null>(),
  preview: z.custom<UploadedFile | null>().optional(),
  whatYouWillLearn: z
    .array(z.string())
    .min(1, "What you will learn is required"),
});

interface CourseInformationFormProps {
  onSubmit: (values: z.infer<typeof formSchema>) => void;
  onSaveAsDraft: (values: z.infer<typeof formSchema>) => void;
  thumbnail: UploadedFile | null;
  setThumbnail: (thumbnail: UploadedFile | null) => void;
  preview: UploadedFile | null;
  setPreview: (preview: UploadedFile | null) => void;
  isSubmitting: boolean;
  form: UseFormReturn<z.infer<typeof formSchema>>;
}

export function CourseInformationForm({
  onSubmit,
  onSaveAsDraft,
  thumbnail,
  setThumbnail,
  preview,
  setPreview,
  isSubmitting,
  form,
}: CourseInformationFormProps) {
  // Helper function to check if section has errors
  const getSectionErrors = (
    fields: Array<keyof z.infer<typeof formSchema>>
  ) => {
    return fields.some((field) => form.formState.errors[field]);
  };

  const mediaErrors = getSectionErrors(["thumbnail", "preview"]);
  const basicDetailsErrors = getSectionErrors([
    "title",
    "shortDescription",
    "description",
    "whatYouWillLearn",
  ]);
  const categoryPricingErrors = getSectionErrors([
    "category",
    "subcategory",
    "price",
    "discountPrice",
  ]);

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-semibold">Course Information</h2>
        <p className="text-sm text-muted-foreground">
          Fill in the basic information about your course
        </p>
      </div>

      <Accordion type="single" collapsible className="space-y-4">
        {/* Media Section */}
        <AccordionItem value="media" className="border rounded-lg">
          <AccordionTrigger className="px-4">
            <div className="flex items-center gap-2">
              <ImageIcon
                className={cn("h-4 w-4", mediaErrors && "text-destructive")}
              />
              <span>Course Media</span>
              {mediaErrors && (
                <AlertCircle className="h-4 w-4 text-destructive ml-2" />
              )}
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4 space-y-6">
            <div>
              <h3 className="text-sm font-medium mb-2 flex items-center gap-2">
                <ImageIcon className="h-4 w-4" />
                Course Thumbnail
              </h3>
              <ThumbnailImageUploader
                uploadedImage={thumbnail}
                onImageChange={setThumbnail}
                className="max-w-[300px]"
              />
              <FormDescription className="mt-2">
                Upload a high-quality image that represents your course (16:9
                ratio recommended)
              </FormDescription>
            </div>

            <div>
              <h3 className="text-sm font-medium mb-2 flex items-center gap-2">
                <PlayCircle className="h-4 w-4" />
                Course Preview Video
              </h3>
              <VideoUploader
                uploadedVideo={preview}
                onVideoChange={setPreview}
                className="max-w-[600px]"
              />
              <FormDescription className="mt-2">
                Upload a short preview video to showcase your course (optional)
              </FormDescription>
            </div>
          </AccordionContent>
        </AccordionItem>

        {/* Basic Details Section */}
        <AccordionItem value="basic-details" className="border rounded-lg">
          <AccordionTrigger className="px-4">
            <div className="flex items-center gap-2">
              <BookOpenIcon
                className={cn(
                  "h-5 w-5",
                  basicDetailsErrors && "text-destructive"
                )}
              />
              <span>Basic Details</span>
              {basicDetailsErrors && (
                <AlertCircle className="h-4 w-4 text-destructive ml-2" />
              )}
            </div>
          </AccordionTrigger>
          <AccordionContent className="space-y-6 px-4 pb-4">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <BookmarkIcon className="h-4 w-4" />
                    Course Title
                  </FormLabel>
                  <FormControl>
                    <Input {...field} className="max-w-2xl" />
                  </FormControl>
                  <FormDescription>
                    A clear and concise title for your course
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="whatYouWillLearn"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>What You Will Learn</FormLabel>
                  <FormControl>
                    <div className="space-y-2">
                      {field.value.map((item, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <Input
                            value={item}
                            onChange={(e) => {
                              const newValue = [...field.value];
                              newValue[index] = e.target.value;
                              field.onChange(newValue);
                            }}
                            className="max-w-2xl"
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              const newValue = field.value.filter(
                                (_, i) => i !== index
                              );
                              field.onChange(newValue);
                            }}
                          >
                            Remove
                          </Button>
                        </div>
                      ))}
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          field.onChange([...field.value, ""]);
                        }}
                      >
                        Add Learning Objective
                      </Button>
                    </div>
                  </FormControl>
                  <FormDescription>
                    List the key learning objectives or outcomes of your course
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="shortDescription"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Short Description</FormLabel>
                  <FormControl>
                    <Textarea {...field} className="max-w-2xl h-20" />
                  </FormControl>
                  <FormDescription>
                    A brief overview of what students will learn (displayed in
                    course cards)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Full Description</FormLabel>
                  <FormControl>
                    <TiptapEditor
                      content={field.value || ""}
                      aiContent={null}
                      onContentChange={field.onChange}
                    />
                  </FormControl>
                  <FormDescription>
                    Detailed description of your course content and learning
                    outcomes
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </AccordionContent>
        </AccordionItem>

        {/* Category & Pricing Section */}
        <AccordionItem value="category-pricing" className="border rounded-lg">
          <AccordionTrigger className="px-4">
            <div className="flex items-center gap-2">
              <FolderIcon
                className={cn(
                  "h-5 w-5",
                  categoryPricingErrors && "text-destructive"
                )}
              />
              <span>Category & Pricing</span>
              {categoryPricingErrors && (
                <AlertCircle className="h-4 w-4 text-destructive ml-2" />
              )}
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4">
            <div className="space-y-6">
              <div className="grid gap-6 sm:grid-cols-2">
                <FormField
                  control={form.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <FolderIcon className="h-4 w-4" />
                        Category
                      </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a category" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {courseCategories.map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                              {category.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Choose the main category for your course
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="subcategory"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Subcategory</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a subcategory" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {courseCategories
                            .find((cat) => cat.id === form.watch("category"))
                            ?.subcategories.map((sub) => (
                              <SelectItem key={sub.id} value={sub.id}>
                                {sub.name}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Choose a more specific category
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid gap-6 sm:grid-cols-2">
                <FormField
                  control={form.control}
                  name="isFree"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Free Course</FormLabel>
                        <FormDescription>
                          Make this course available for free
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <DollarSignIcon className="h-4 w-4" />
                        Price
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          {...field}
                          onChange={(e) =>
                            field.onChange(parseFloat(e.target.value))
                          }
                          className="max-w-[200px]"
                        />
                      </FormControl>
                      <FormDescription>
                        Set the price for your course
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="discountPrice"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Discount Price</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          value={field.value ?? ""}
                          onChange={(e) => {
                            const value = e.target.value;
                            field.onChange(
                              value === "" ? undefined : parseFloat(value)
                            );
                          }}
                          className="max-w-[200px]"
                        />
                      </FormControl>
                      <FormDescription>
                        Optional: Set a discounted price
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
