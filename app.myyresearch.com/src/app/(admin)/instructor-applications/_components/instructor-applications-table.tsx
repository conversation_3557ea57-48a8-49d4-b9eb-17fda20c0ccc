"use client";

import * as React from "react";
import {
  ApiStatus,
  type DataTableAdvanced<PERSON>ilter<PERSON>ield,
  type DataTableFilter<PERSON>ield,
  type DataTableRowAction,
} from "@/types";

import {
  type InstructorApplication,
  ApplicationStatus,
} from "@/types/instructor-application";
import { toSentenceCase } from "@/lib/data-table/utils";
import { useDataTable } from "@/hooks/data-table/use-data-table";
import { DataTable } from "@/components/data-table/data-table";
import { DataTableAdvancedToolbar } from "@/components/data-table/data-table-advanced-toolbar";
import { DataTableToolbar } from "@/components/data-table/data-table-toolbar";

import { DeleteInstructorApplicationsDialog } from "./delete-instructor-applications-dialog";
import { getColumns } from "./instructor-applications-table-columns";
import { InstructorApplicationsTableFloatingBar } from "./instructor-applications-table-floating-bar";
import { InstructorApplicationsTableToolbarActions } from "./instructor-applications-table-toolbar-actions";
import { useRouter } from "next/navigation";

interface InstructorApplicationsTableProps {
  data: InstructorApplication[];
  pageCount: number;
}

export function InstructorApplicationsTable({
  data,
  pageCount,
}: InstructorApplicationsTableProps) {
  const router = useRouter();

  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<InstructorApplication> | null>(null);

  const handleDelete = React.useCallback(
    async (id: string) => {
      // TODO: Implement delete functionality
      router.refresh();
    },
    [router]
  );

  const columns = React.useMemo(
    () => getColumns({ setRowAction, onDelete: handleDelete }),
    [setRowAction, handleDelete]
  );

  const filterFields: DataTableFilterField<InstructorApplication>[] = [
    {
      id: "firstName",
      label: "First Name",
      placeholder: "Filter by first name...",
    },
    {
      id: "lastName",
      label: "Last Name",
      placeholder: "Filter by last name...",
    },
    {
      id: "status",
      label: "Status",
      options: Object.values(ApplicationStatus).map((status) => ({
        label: toSentenceCase(status.replace("_", " ")),
        value: status,
      })),
    },
  ];

  const advancedFilterFields: DataTableAdvancedFilterField<InstructorApplication>[] =
    [
      {
        id: "firstName",
        label: "First Name",
        type: "text",
      },
      {
        id: "lastName",
        label: "Last Name",
        type: "text",
      },
      {
        id: "email",
        label: "Email",
        type: "text",
      },
      {
        id: "status",
        label: "Status",
        type: "multi-select",
        options: Object.values(ApplicationStatus).map((status) => ({
          label: toSentenceCase(status.replace("_", " ")),
          value: status,
        })),
      },
      {
        id: "createdAt",
        label: "Applied at",
        type: "date",
      },
    ];

  const { table } = useDataTable({
    data,
    columns,
    pageCount,
    filterFields,
    enableAdvancedFilter: true,
    initialState: {
      sorting: [{ id: "createdAt", desc: true }],
      columnPinning: { right: ["actions"] },
    },
    getRowId: (originalRow) => originalRow._id,
    shallow: false,
    clearOnDefault: true,
  });

  return (
    <>
      <DataTable
        table={table}
        floatingBar={<InstructorApplicationsTableFloatingBar table={table} />}
      >
        <DataTableAdvancedToolbar
          table={table}
          filterFields={advancedFilterFields}
          shallow={false}
        >
          <InstructorApplicationsTableToolbarActions
            table={table}
            onRefresh={() => router.refresh()}
          />
        </DataTableAdvancedToolbar>
      </DataTable>
      <DeleteInstructorApplicationsDialog
        open={rowAction?.type === "delete"}
        onOpenChange={() => setRowAction(null)}
        applications={rowAction?.row.original ? [rowAction?.row.original] : []}
        showTrigger={false}
        onSuccess={() => rowAction?.row.toggleSelected(false)}
      />
    </>
  );
}
