"use client";

import { type Table } from "@tanstack/react-table";
import { Download } from "lucide-react";

import { type InstructorApplication } from "@/types/instructor-application";
import { exportTableToCSV } from "@/lib/data-table/export";
import { Button } from "@/components/ui/button";

import { DeleteInstructorApplicationsDialog } from "./delete-instructor-applications-dialog";

interface InstructorApplicationsTableToolbarActionsProps {
  table: Table<InstructorApplication>;
  onRefresh?: () => void;
}

export function InstructorApplicationsTableToolbarActions({
  table,
  onRefresh,
}: InstructorApplicationsTableToolbarActionsProps) {
  return (
    <div className="flex items-center gap-2">
      {table.getFilteredSelectedRowModel().rows.length > 0 ? (
        <DeleteInstructorApplicationsDialog
          applications={table
            .getFilteredSelectedRowModel()
            .rows.map((row) => row.original)}
          onSuccess={() => {
            table.toggleAllRowsSelected(false);
            onRefresh?.();
          }}
        />
      ) : null}
      <Button
        variant="outline"
        size="sm"
        onClick={() =>
          exportTableToCSV(table, {
            filename: "instructor-applications",
            excludeColumns: ["select", "actions"],
          })
        }
        className="gap-2"
      >
        <Download className="size-4" aria-hidden="true" />
        Export
      </Button>
    </div>
  );
}
