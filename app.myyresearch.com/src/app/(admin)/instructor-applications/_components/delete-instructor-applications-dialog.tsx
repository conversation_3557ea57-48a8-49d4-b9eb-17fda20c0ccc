"use client";

import * as React from "react";
import { type Row } from "@tanstack/react-table";
import { Loader, Trash } from "lucide-react";
import { toast } from "sonner";

import { type InstructorApplication } from "@/types/instructor-application";
import { useMediaQuery } from "@/hooks/data-table/use-media-query";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from "@/components/ui/drawer";

interface DeleteInstructorApplicationsDialogProps
  extends React.ComponentPropsWithoutRef<typeof Dialog> {
  applications: Row<InstructorApplication>["original"][];
  showTrigger?: boolean;
  onSuccess?: () => void;
}

export function DeleteInstructorApplicationsDialog({
  applications,
  showTrigger = true,
  onSuccess,
  ...props
}: DeleteInstructorApplicationsDialogProps) {
  const [open, setOpen] = React.useState(false);
  const [isPending, startTransition] = React.useTransition();

  const isDesktop = useMediaQuery("(min-width: 768px)");

  function onDelete() {
    startTransition(async () => {
      try {
        // TODO: Implement delete functionality
        setOpen(false);
        onSuccess?.();
        toast.success("Applications deleted successfully");
      } catch (error) {
        toast.error("Something went wrong");
      }
    });
  }

  if (isDesktop) {
    return (
      <Dialog open={open} onOpenChange={setOpen} {...props}>
        {showTrigger && (
          <DialogTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="gap-2 text-destructive"
            >
              <Trash className="size-4" aria-hidden="true" />
              Delete
            </Button>
          </DialogTrigger>
        )}
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Applications</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete {applications.length}{" "}
              application(s)? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline" disabled={isPending}>
                Cancel
              </Button>
            </DialogClose>
            <Button
              variant="destructive"
              disabled={isPending}
              onClick={() => onDelete()}
            >
              {isPending && (
                <Loader
                  className="mr-2 size-4 animate-spin"
                  aria-hidden="true"
                />
              )}
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      {showTrigger && (
        <DrawerTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="gap-2 text-destructive"
          >
            <Trash className="size-4" aria-hidden="true" />
            Delete
          </Button>
        </DrawerTrigger>
      )}
      <DrawerContent>
        <DrawerHeader>
          <DrawerTitle>Delete Applications</DrawerTitle>
          <DrawerDescription>
            Are you sure you want to delete {applications.length}{" "}
            application(s)? This action cannot be undone.
          </DrawerDescription>
        </DrawerHeader>
        <DrawerFooter>
          <DrawerClose asChild>
            <Button variant="outline" disabled={isPending}>
              Cancel
            </Button>
          </DrawerClose>
          <Button
            variant="destructive"
            disabled={isPending}
            onClick={() => onDelete()}
          >
            {isPending && (
              <Loader className="mr-2 size-4 animate-spin" aria-hidden="true" />
            )}
            Delete
          </Button>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}
