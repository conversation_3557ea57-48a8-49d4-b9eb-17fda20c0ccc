import { Shell } from "@/components/shell";
import { ContentLayout } from "@/components/admin-panel/content-layout";
import { getInstructorApplication } from "@/lib/instructor-applications";
import { notFound } from "next/navigation";
import { ApiStatus } from "@/types";
import { ApplicationDetails } from "./_components/application-details";

interface InstructorApplicationDetailsPageProps {
  params: Promise<{
    id: string;
  }>;
}

export const dynamic = "force-dynamic";
export const revalidate = 0;

export default async function InstructorApplicationDetailsPage({
  params,
}: InstructorApplicationDetailsPageProps) {
  const response = await getInstructorApplication((await params).id);

  if (response.status !== ApiStatus.SUCCESS) {
    return notFound();
  }

  return (
    <ContentLayout title="Application Details">
      <Shell>
        <div className="space-y-6">
          <div>
            <h1 className="text-2xl font-semibold tracking-tight">
              Application Details
            </h1>
            <p className="text-sm text-muted-foreground">
              Review and manage instructor application
            </p>
          </div>

          <ApplicationDetails application={response.data} />
        </div>
      </Shell>
    </ContentLayout>
  );
}
