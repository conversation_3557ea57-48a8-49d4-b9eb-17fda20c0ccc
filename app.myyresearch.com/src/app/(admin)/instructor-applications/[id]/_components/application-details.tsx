"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { ApiStatus } from "@/types";
import {
  type InstructorApplication,
  ApplicationStatus,
} from "@/types/instructor-application";
import { formatDate } from "@/lib/data-table/utils";
import { updateApplicationStatus } from "@/lib/instructor-applications";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";

interface ApplicationDetailsProps {
  application: InstructorApplication;
}

export function ApplicationDetails({ application }: ApplicationDetailsProps) {
  const router = useRouter();
  const [status, setStatus] = React.useState<ApplicationStatus>(
    application.status
  );
  const [notes, setNotes] = React.useState("");
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const handleStatusUpdate = async () => {
    try {
      setIsSubmitting(true);
      const response = await updateApplicationStatus(application._id, {
        status,
        reviewNotes: notes,
      });

      if (response.status === ApiStatus.SUCCESS) {
        toast.success("Application status updated successfully");
        router.refresh();
      } else {
        toast.error(response.message || "Failed to update application status");
      }
    } catch (error) {
      toast.error("Something went wrong");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="grid gap-6">
      <Card>
        <CardHeader>
          <CardTitle>Personal Information</CardTitle>
        </CardHeader>
        <CardContent className="grid gap-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>First Name</Label>
              <p className="text-sm">{application.firstName}</p>
            </div>
            <div>
              <Label>Last Name</Label>
              <p className="text-sm">{application.lastName}</p>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>Email</Label>
              <p className="text-sm">{application.email}</p>
            </div>
            <div>
              <Label>Phone</Label>
              <p className="text-sm">{application.phone}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Application Details</CardTitle>
        </CardHeader>
        <CardContent className="grid gap-4">
          <div>
            <Label>Description</Label>
            <p className="text-sm whitespace-pre-wrap">
              {application.description}
            </p>
          </div>
          <div>
            <Label>Educational Levels</Label>
            <div className="flex flex-wrap gap-2 mt-1">
              {application.educationalLevels.map((level) => (
                <Badge key={level} variant="secondary">
                  {level}
                </Badge>
              ))}
            </div>
          </div>
          <div>
            <Label>Occupations</Label>
            <div className="flex flex-wrap gap-2 mt-1">
              {application.occupations.map((occupation) => (
                <Badge key={occupation} variant="secondary">
                  {occupation}
                </Badge>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Application Status</CardTitle>
        </CardHeader>
        <CardContent className="grid gap-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>Current Status</Label>
              <div className="mt-1">
                <Badge
                  variant={
                    {
                      [ApplicationStatus.PENDING]: "secondary",
                      [ApplicationStatus.APPROVED]: "default",
                      [ApplicationStatus.REJECTED]: "destructive",
                      [ApplicationStatus.UNDER_REVIEW]: "warning",
                    }[application.status] as any
                  }
                >
                  {application.status.replace("_", " ")}
                </Badge>
              </div>
            </div>
            <div>
              <Label>Applied At</Label>
              <p className="text-sm">{formatDate(application.createdAt)}</p>
            </div>
          </div>

          {application.reviewedBy && (
            <>
              <Separator />
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Reviewed By</Label>
                  <p className="text-sm">{application.reviewedBy}</p>
                </div>
                <div>
                  <Label>Reviewed At</Label>
                  <p className="text-sm">
                    {application.reviewedAt
                      ? formatDate(application.reviewedAt)
                      : "N/A"}
                  </p>
                </div>
              </div>
              <div>
                <Label>Review Notes</Label>
                <p className="text-sm whitespace-pre-wrap">
                  {application.reviewNotes || "No notes provided"}
                </p>
              </div>
            </>
          )}

          <Separator />

          <div className="grid gap-4">
            <div>
              <Label>Update Status</Label>
              <Select
                value={status}
                onValueChange={(value: ApplicationStatus) => setStatus(value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.values(ApplicationStatus).map((status) => (
                    <SelectItem key={status} value={status}>
                      {status.replace("_", " ")}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label>Review Notes</Label>
              <Textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Add your review notes here..."
                className="mt-1"
              />
            </div>
            <Button
              onClick={handleStatusUpdate}
              disabled={isSubmitting}
              className="w-full"
            >
              Update Status
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
