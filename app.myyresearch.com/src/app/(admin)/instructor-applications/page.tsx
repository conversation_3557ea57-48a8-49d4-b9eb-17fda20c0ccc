import { Shell } from "@/components/shell";
import { ContentLayout } from "@/components/admin-panel/content-layout";
import { getInstructorApplications } from "@/lib/instructor-applications";
import { InstructorApplicationsTable } from "./_components/instructor-applications-table";
import { ApiStatus } from "@/types";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export default async function InstructorApplicationsPage() {
  const response = await getInstructorApplications();

  return (
    <ContentLayout title="Instructor Applications">
      <Shell>
        <div className="space-y-6">
          <div>
            <h1 className="text-2xl font-semibold tracking-tight">
              Instructor Applications
            </h1>
            <p className="text-sm text-muted-foreground">
              Manage instructor applications and their status
            </p>
          </div>

          <InstructorApplicationsTable
            data={response.status === ApiStatus.SUCCESS ? response.data : []}
            pageCount={1} // TODO: Implement pagination
          />
        </div>
      </Shell>
    </ContentLayout>
  );
}
