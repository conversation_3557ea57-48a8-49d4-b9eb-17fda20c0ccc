import * as React from "react";
import { type SearchParams } from "@/types";
import { ContentLayout } from "@/components/admin-panel/content-layout";
import Link from "next/link";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

import { Skeleton } from "@/components/ui/skeleton";
import { DataTableSkeleton } from "@/components/data-table/data-table-skeleton";
import { DateRangePicker } from "@/components/date-range-picker";
import { Shell } from "@/components/shell";

import { NewslettersTable } from "./_components/newsletters-table";
import { searchParamsCache } from "./_lib/validations";
import { getValidFilters } from "@/lib/data-table/data-table";
import { type Metadata } from "next";
import { getNewsletters, getNewsletterStatusCounts } from "./_lib/queries";
import { FeatureFlagsProvider } from "./_components/feature-flags-provider";

export const metadata: Metadata = {
  title: "Newsletters",
  description: "Manage newsletter subscriptions",
};

interface IndexPageProps {
  searchParams: Promise<SearchParams>;
}

export default async function IndexPage(props: IndexPageProps) {
  const searchParams = await props.searchParams;
  const search = searchParamsCache.parse(searchParams);
  const validFilters = getValidFilters(search.filters);
  const promises = Promise.all([
    getNewsletters({
      ...search,
      filters: validFilters,
    }),
    getNewsletterStatusCounts({
      ...search,
      filters: validFilters,
    }),
  ]);

  return (
    <ContentLayout title="Newsletters">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">Home</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Newsletters</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <Shell className="gap-2">
        <FeatureFlagsProvider>
          <React.Suspense fallback={<Skeleton className="h-7 w-52" />}>
            <DateRangePicker
              triggerSize="sm"
              triggerClassName="ml-auto w-56 sm:w-60"
              align="end"
            />
          </React.Suspense>
          <React.Suspense
            fallback={
              <DataTableSkeleton
                columnCount={6}
                searchableColumnCount={1}
                filterableColumnCount={1}
                cellWidths={[
                  "15rem",
                  "12rem",
                  "12rem",
                  "12rem",
                  "12rem",
                  "8rem",
                ]}
                shrinkZero
              />
            }
          >
            <NewslettersTable promises={promises} />
          </React.Suspense>
        </FeatureFlagsProvider>
      </Shell>
    </ContentLayout>
  );
}
