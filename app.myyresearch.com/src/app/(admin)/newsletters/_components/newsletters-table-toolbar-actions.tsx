"use client";

import { type Table } from "@tanstack/react-table";
import { Download } from "lucide-react";

import { type Newsletter } from "@/types/newsletters";
import { exportTableToCSV } from "@/lib/data-table/export";
import { Button } from "@/components/ui/button";
import { DeleteNewslettersDialog } from "./delete-newsletters-dialog";

interface NewslettersTableToolbarActionsProps {
  table: Table<Newsletter>;
  onRefresh?: () => void;
}

export function NewslettersTableToolbarActions({
  table,
  onRefresh,
}: NewslettersTableToolbarActionsProps) {
  return (
    <div className="flex items-center gap-2">
      <Button
        variant="outline"
        size="sm"
        onClick={() =>
          exportTableToCSV(table, {
            filename: "newsletters",
            excludeColumns: ["select", "actions"],
          })
        }
        className="gap-2"
      >
        <Download className="size-4" aria-hidden="true" />
        Export
      </Button>
      {table.getFilteredSelectedRowModel().rows.length > 0 ? (
        <DeleteNewslettersDialog
          newsletters={table
            .getFilteredSelectedRowModel()
            .rows.map((row) => row.original)}
          onSuccess={() => {
            table.toggleAllRowsSelected(false);
            onRefresh?.();
          }}
        />
      ) : null}
    </div>
  );
}
