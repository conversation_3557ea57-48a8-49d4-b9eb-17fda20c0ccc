import "server-only";

import { type Task } from "@/types/task";
import { unstable_cache } from "@/lib/data-table/unstable-cache";

import { type GetTasksSchema } from "./validations";
import { tasks } from "../_data/tasks";
import { tasks as tasksData } from "../_data/tasks";

export async function getTasks(input: GetTasksSchema): Promise<{
  data: Task[];
  pageCount: number;
}> {
  return unstable_cache(
    async () => {
      try {
        // Filter tasks based on input criteria
        let filteredTasks = [...tasksData];

        // Apply basic filters
        if (!input.flags.includes("advancedTable")) {
          if (input.title) {
            filteredTasks = filteredTasks.filter((task) =>
              task.title?.toLowerCase().includes(input.title.toLowerCase())
            );
          }

          if (input.status.length > 0) {
            filteredTasks = filteredTasks.filter((task) =>
              input.status.includes(task.status)
            );
          }

          if (input.priority.length > 0) {
            filteredTasks = filteredTasks.filter((task) =>
              input.priority.includes(task.priority)
            );
          }

          if (input.from) {
            const fromDate = new Date(input.from);
            filteredTasks = filteredTasks.filter(
              (task) => task.createdAt >= fromDate
            );
          }

          if (input.to) {
            const toDate = new Date(input.to);
            filteredTasks = filteredTasks.filter(
              (task) => task.createdAt <= toDate
            );
          }
        }

        // Apply sorting
        if (input.sort.length > 0) {
          // Sorting logic here
        } else {
          filteredTasks.sort(
            (a, b) => a.createdAt.getTime() - b.createdAt.getTime()
          );
        }

        // Handle pagination
        const total = filteredTasks.length;
        const pageCount = Math.ceil(total / input.perPage);
        const offset = (input.page - 1) * input.perPage;
        const paginatedTasks = filteredTasks.slice(
          offset,
          offset + input.perPage
        );

        return {
          data: paginatedTasks,
          pageCount,
        };
      } catch (err) {
        return { data: [], pageCount: 0 };
      }
    },
    [JSON.stringify(input)],
    {
      revalidate: 3600,
      tags: ["tasks"],
    }
  )();
}

export async function getTaskStatusCounts() {
  return unstable_cache(
    async () => {
      try {
        const statusCounts: Record<Task["status"], number> = {} as Record<
          Task["status"],
          number
        >;

        tasksData.forEach((task) => {
          statusCounts[task.status] = (statusCounts[task.status] || 0) + 1;
        });

        return statusCounts;
      } catch (err) {
        return {} as Record<Task["status"], number>;
      }
    },
    ["task-status-counts"],
    {
      revalidate: 3600,
    }
  )();
}

export async function getTaskPriorityCounts() {
  return unstable_cache(
    async () => {
      try {
        const priorityCounts: Record<Task["priority"], number> = {} as Record<
          Task["priority"],
          number
        >;

        tasksData.forEach((task) => {
          priorityCounts[task.priority] =
            (priorityCounts[task.priority] || 0) + 1;
        });

        return priorityCounts;
      } catch (err) {
        return {} as Record<Task["priority"], number>;
      }
    },
    ["task-priority-counts"],
    {
      revalidate: 3600,
    }
  )();
}
