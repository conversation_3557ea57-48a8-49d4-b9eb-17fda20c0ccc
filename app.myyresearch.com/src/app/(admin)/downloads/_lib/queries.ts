"use server";

import { type OrderItemDetails } from "@/types/orders";
import {
  getTemplateDownloads,
  getOrderItemsCategoryCounts,
} from "@/lib/order-items";
import { ApiStatus } from "@/types/common";
import { type GetPurchasedTemplatesSchema } from "./validations";

export async function getPurchasedTemplates(
  input: GetPurchasedTemplatesSchema
): Promise<{
  data: {
    templates: OrderItemDetails[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
  pageCount: number;
}> {
  try {
    const params = {
      search: input.title,
      category: input.category.length > 0 ? input.category[0] : undefined,
      subCategory:
        input.subCategory.length > 0 ? input.subCategory[0] : undefined,
      limit: input.perPage || 10,
      page: input.page || 1,
      from: input.from || undefined,
      to: input.to || undefined,
    };

    const response = await getTemplateDownloads(params);

    if (response.status === ApiStatus.SUCCESS && response.data) {
      return {
        data: {
          templates: response.data.data as OrderItemDetails[],
          total: response.data.total,
          page: response.data.page,
          limit: params.limit,
          totalPages: response.data.totalPages,
        },
        pageCount: response.data.totalPages,
      };
    }

    return {
      data: {
        templates: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      },
      pageCount: 0,
    };
  } catch (error) {
    console.error("Error fetching template downloads:", error);
    return {
      data: {
        templates: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      },
      pageCount: 0,
    };
  }
}

export async function getPurchasedTemplateCategoryCounts(): Promise<
  Array<{ category: string; count: number }>
> {
  try {
    return await getOrderItemsCategoryCounts();
  } catch (error) {
    console.error("Error fetching template category counts:", error);
    return [];
  }
}
