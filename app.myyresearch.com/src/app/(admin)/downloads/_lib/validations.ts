import {
  createSearchParamsCache,
  parseAsArrayOf,
  parseAsInteger,
  parseAsString,
  parseAsStringEnum,
} from "nuqs/server";
import * as z from "zod";

import { type PurchasedTemplate, CreatorType } from "@/types/purchased-items";
import {
  getFiltersStateParser,
  getSortingStateParser,
} from "@/lib/data-table/parsers";

export const searchParamsCache = createSearchParamsCache({
  flags: parseAsArrayOf(z.enum(["advancedTable", "floatingBar"])).withDefault(
    []
  ),
  page: parseAsInteger.withDefault(1),
  perPage: parseAsInteger.withDefault(10),
  sort: getSortingStateParser<PurchasedTemplate>().withDefault([
    { id: "purchasedDate", desc: true },
  ]),
  title: parseAsString.withDefault(""),
  category: parseAsArrayOf(parseAsString).withDefault([]),
  subCategory: parseAsArrayOf(parseAsString).withDefault([]),
  creatorType: parseAsArrayOf(parseAsStringEnum(Object.values(CreatorType))).withDefault([]),
  from: parseAsString.withDefault(""),
  to: parseAsString.withDefault(""),
  // Advanced filter
  filters: getFiltersStateParser().withDefault([]),
});

export type GetPurchasedTemplatesSchema = Awaited<
  ReturnType<typeof searchParamsCache.parse>
>;

export const createPurchasedTemplateSchema = z.object({
  title: z.string().min(1, "Title is required"),
  category: z.string().min(1, "Category is required"),
  subCategory: z.string().min(1, "Sub-category is required"),
  creatorType: z.nativeEnum(CreatorType),
  sellingPrice: z.number().min(0, "Price must be non-negative"),
});

export const updatePurchasedTemplateSchema = createPurchasedTemplateSchema.partial();

export type CreatePurchasedTemplateSchema = z.infer<typeof createPurchasedTemplateSchema>;
export type UpdatePurchasedTemplateSchema = z.infer<typeof updatePurchasedTemplateSchema>;
