"use client";

import { type Table } from "@tanstack/react-table";
import { Download } from "lucide-react";

import { type MinimalTemplateDownload } from "@/types/download";
import { exportTableToCSV } from "@/lib/data-table/export";
import { Button } from "@/components/ui/button";

interface DownloadsTableToolbarActionsProps {
  table: Table<MinimalTemplateDownload>;
  onRefresh?: () => void;
}

export function DownloadsTableToolbarActions({
  table,
  onRefresh,
}: DownloadsTableToolbarActionsProps) {
  return (
    <div className="flex items-center gap-2">
      <Button
        variant="outline"
        size="sm"
        onClick={() =>
          exportTableToCSV(table, {
            filename: "template-downloads",
            excludeColumns: ["select", "actions"],
          })
        }
        className="gap-2"
      >
        <Download className="size-4" aria-hidden="true" />
        Export
      </Button>
    </div>
  );
}
