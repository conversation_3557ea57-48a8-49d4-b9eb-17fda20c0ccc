"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import type {
  DataTableAdvancedFilterField,
  DataTableFilterField,
  DataTableRowAction,
} from "@/types";

import { DownloadStatus, type MinimalTemplateDownload } from "@/types/download";
import { toSentenceCase } from "@/lib/data-table/utils";
import { useDataTable } from "@/hooks/data-table/use-data-table";
import { DataTable } from "@/components/data-table/data-table";
import { DataTableAdvancedToolbar } from "@/components/data-table/data-table-advanced-toolbar";
import { DataTableToolbar } from "@/components/data-table/data-table-toolbar";

import { getColumns } from "./downloads-table-columns";
import { DownloadsTableToolbarActions } from "./downloads-table-toolbar-actions";

interface DownloadsTableProps {
  data: MinimalTemplateDownload[];
  pageCount: number;
  statusCounts: Record<DownloadStatus, number>;
}

export function DownloadsTable({
  data,
  pageCount,
  statusCounts,
}: DownloadsTableProps) {
  const router = useRouter();

  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<MinimalTemplateDownload> | null>(null);

  const columns = React.useMemo(
    () => getColumns({ setRowAction }),
    [setRowAction]
  );

  const filterFields: DataTableFilterField<MinimalTemplateDownload>[] = [
    {
      id: "templateTitle",
      label: "Template",
      placeholder: "Filter templates...",
    },
    {
      id: "user",
      label: "User",
      placeholder: "Filter users...",
    },
    {
      id: "status",
      label: "Status",
      options: Object.values(DownloadStatus).map((status) => ({
        label: toSentenceCase(status),
        value: status,
        count: statusCounts[status] || 0,
      })),
    },
  ];

  const advancedFilterFields: DataTableAdvancedFilterField<MinimalTemplateDownload>[] =
    [
      {
        id: "templateTitle",
        label: "Template",
        type: "text",
      },
      {
        id: "user",
        label: "User",
        type: "text",
      },
      {
        id: "status",
        label: "Status",
        type: "multi-select",
        options: Object.values(DownloadStatus).map((status) => ({
          label: toSentenceCase(status),
          value: status,
          count: statusCounts[status] || 0,
        })),
      },
      {
        id: "createdAt",
        label: "Created at",
        type: "date",
      },
      {
        id: "lastAccessedAt",
        label: "Last accessed",
        type: "date",
      },
    ];

  const { table } = useDataTable({
    data,
    columns,
    pageCount,
    filterFields,
    enableAdvancedFilter: true,
    initialState: {
      sorting: [{ id: "createdAt", desc: true }],
      columnPinning: { right: ["actions"] },
    },
    getRowId: (originalRow) => originalRow._id,
  });

  return (
    <DataTable table={table}>
      <DataTableAdvancedToolbar
        table={table}
        filterFields={advancedFilterFields}
      >
        <DownloadsTableToolbarActions
          table={table}
          onRefresh={() => router.refresh()}
        />
      </DataTableAdvancedToolbar>
    </DataTable>
  );
}
