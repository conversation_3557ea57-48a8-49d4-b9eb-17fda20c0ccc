"use client";

import * as React from "react";
import { type ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DataTable } from "@/components/data-table/data-table";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { DataTableToolbar } from "@/components/data-table/data-table-toolbar";
import { useDataTable } from "@/hooks/data-table/use-data-table";
import { PurchasedTemplate, CreatorType } from "@/types/purchased-items";
import { formatDate } from "@/lib/data-table/utils";
import { MoreH<PERSON>zontal, Eye, Download } from "lucide-react";
import { type DataTableFilterField } from "@/types";

interface PurchasedTemplatesTableProps {
  data: PurchasedTemplate[];
  pageCount: number;
}

export function PurchasedTemplatesTable({
  data,
  pageCount,
}: PurchasedTemplatesTableProps) {
  const columns = React.useMemo<ColumnDef<PurchasedTemplate>[]>(
    () => [
      {
        id: "select",
        header: ({ table }) => (
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && "indeterminate")
            }
            onCheckedChange={(value) =>
              table.toggleAllPageRowsSelected(!!value)
            }
            aria-label="Select all"
            className="translate-y-0.5"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
            className="translate-y-0.5"
          />
        ),
        enableSorting: false,
        enableHiding: false,
      },
      {
        accessorKey: "publicId",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Template ID" />
        ),
        cell: ({ row }) => (
          <div className="font-medium">{row.getValue("publicId")}</div>
        ),
      },
      {
        accessorKey: "title",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Title" />
        ),
        cell: ({ row }) => {
          return (
            <div className="flex space-x-2">
              <span className="max-w-[200px] truncate font-medium">
                {row.getValue("title")}
              </span>
            </div>
          );
        },
      },
      {
        accessorKey: "category",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Category" />
        ),
        cell: ({ row }) => (
          <Badge variant="outline">{row.getValue("category")}</Badge>
        ),
      },
      {
        accessorKey: "sellingPrice",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Price" />
        ),
        cell: ({ row }) => {
          const price = row.getValue("sellingPrice") as number;
          return <div className="font-medium">${price.toFixed(2)}</div>;
        },
      },
      {
        accessorKey: "creatorType",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Creator" />
        ),
        cell: ({ row }) => {
          const creatorType = row.getValue("creatorType") as CreatorType;
          return (
            <Badge
              variant={
                creatorType === CreatorType.MYRESEARCH ? "default" : "secondary"
              }
            >
              {creatorType}
            </Badge>
          );
        },
      },
      {
        accessorKey: "purchasedDate",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Purchased Date" />
        ),
        cell: ({ row }) => {
          const date = row.getValue("purchasedDate") as Date;
          return <div>{formatDate(date)}</div>;
        },
      },
      {
        accessorKey: "orderId",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Order ID" />
        ),
        cell: ({ row }) => (
          <div className="font-mono text-sm">{row.getValue("orderId")}</div>
        ),
      },
      {
        id: "actions",
        cell: ({ row }) => {
          return (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  aria-label="Open menu"
                  variant="ghost"
                  className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
                >
                  <MoreHorizontal className="h-4 w-4" />
                  <span className="sr-only">Open menu</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-[160px]">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Download className="mr-2 h-4 w-4" />
                  Download
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          );
        },
      },
    ],
    []
  );

  const filterFields: DataTableFilterField<PurchasedTemplate>[] = [
    {
      id: "title",
      label: "Title",
      placeholder: "Search titles...",
    },
    {
      id: "publicId",
      label: "Template ID",
      placeholder: "Search template IDs...",
    },
    {
      id: "category",
      label: "Category",
      options: [], // Will be populated dynamically
    },
    {
      id: "creatorType",
      label: "Creator Type",
      options: [
        { label: "MyResearch", value: CreatorType.MYRESEARCH },
        { label: "Instructor", value: CreatorType.INSTRUCTOR },
      ],
    },
  ];

  const { table } = useDataTable({
    data,
    columns,
    pageCount,
    filterFields,
    enableAdvancedFilter: false,
    initialState: {
      sorting: [{ id: "purchasedDate", desc: true }],
      columnPinning: { right: ["actions"] },
    },
    getRowId: (originalRow) => originalRow._id,
    shallow: false,
    clearOnDefault: true,
  });

  return (
    <DataTable table={table}>
      <DataTableToolbar table={table} filterFields={filterFields} />
    </DataTable>
  );
}
