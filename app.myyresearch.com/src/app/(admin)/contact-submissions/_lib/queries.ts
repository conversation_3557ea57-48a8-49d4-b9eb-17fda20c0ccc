"use server";

import { ApiStatus } from "@/types/common";
import { type ContactSubmission } from "@/types/contact-submissions";
import { type GetContactSubmissionsSchema } from "./validations";
import {
  getAdminContactSubmissions,
  getContactStatusCounts as getContactStatusCountsApi,
} from "@/lib/contact-us";

export async function getContactSubmissions(input: GetContactSubmissionsSchema): Promise<{
  data: ContactSubmission[];
  pageCount: number;
}> {
  try {
    const params = {
      search: input.search || undefined,
      status: input.status.length > 0 ? input.status[0] : undefined,
      email: input.email || undefined,
      limit: input.perPage || 10,
      page: input.page || 1,
      from: input.from || undefined,
      to: input.to || undefined,
    };
    
    const response = await getAdminContactSubmissions(params);
    if (response.status === ApiStatus.SUCCESS && response.data) {
      return {
        data: response.data.submissions,
        pageCount: response.data.totalPages,
      };
    }

    return { data: [], pageCount: 0 };
  } catch {
    return { data: [], pageCount: 0 };
  }
}

export async function getContactStatusCounts(
  input: GetContactSubmissionsSchema
): Promise<Record<"new" | "in_progress" | "resolved" | "closed", number>> {
  try {
    const response = await getContactStatusCountsApi();
    
    if (response.status === ApiStatus.SUCCESS && response.data) {
      return response.data.counts;
    }

    return { new: 0, in_progress: 0, resolved: 0, closed: 0 };
  } catch {
    return { new: 0, in_progress: 0, resolved: 0, closed: 0 };
  }
}
