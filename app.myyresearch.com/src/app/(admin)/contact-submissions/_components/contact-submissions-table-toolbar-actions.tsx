"use client";

import { type Table } from "@tanstack/react-table";
import { RotateCcw } from "lucide-react";

import { type ContactSubmission } from "@/types/contact-submissions";
import { Button } from "@/components/ui/button";

interface ContactSubmissionsTableToolbarActionsProps {
  table: Table<ContactSubmission>;
  onRefresh?: () => void;
}

export function ContactSubmissionsTableToolbarActions({
  table,
  onRefresh,
}: ContactSubmissionsTableToolbarActionsProps) {
  return (
    <div className="flex items-center gap-2">
      {onRefresh && (
        <Button
          variant="outline"
          size="sm"
          onClick={onRefresh}
          className="h-8 px-2 lg:px-3"
        >
          <RotateCcw className="mr-2 size-4" />
          Refresh
        </Button>
      )}
    </div>
  );
}
