"use client";

import * as React from "react";
import { Trash2 } from "lucide-react";

import { type ContactSubmission } from "@/types/contact-submissions";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface DeleteContactSubmissionsDialogProps {
  submissions: ContactSubmission[];
  showTrigger?: boolean;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: () => void;
}

export function DeleteContactSubmissionsDialog({
  submissions,
  showTrigger = true,
  open,
  onOpenChange,
  onSuccess,
}: DeleteContactSubmissionsDialogProps) {
  const [isDeletePending, setIsDeletePending] = React.useState(false);

  function onDelete() {
    setIsDeletePending(true);
    
    // TODO: Implement actual delete functionality
    setTimeout(() => {
      setIsDeletePending(false);
      onSuccess?.();
      onOpenChange?.(false);
    }, 1000);
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {showTrigger && (
        <DialogTrigger asChild>
          <Button variant="outline" size="sm">
            <Trash2 className="mr-2 size-4" />
            Delete ({submissions.length})
          </Button>
        </DialogTrigger>
      )}
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Are you absolutely sure?</DialogTitle>
          <DialogDescription>
            This action cannot be undone. This will permanently delete{" "}
            <span className="font-medium">{submissions.length}</span>
            {submissions.length === 1 ? " contact submission" : " contact submissions"}{" "}
            from the database.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange?.(false)}>
            Cancel
          </Button>
          <Button
            aria-label={`Delete ${submissions.length} contact submission${
              submissions.length === 1 ? "" : "s"
            }`}
            variant="destructive"
            onClick={onDelete}
            disabled={isDeletePending}
          >
            {isDeletePending ? "Deleting..." : "Delete"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
