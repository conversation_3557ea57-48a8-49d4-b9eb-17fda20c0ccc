"use client";

import * as React from "react";
import { type Table } from "@tanstack/react-table";
import { Trash2 } from "lucide-react";

import { type ContactSubmission } from "@/types/contact-submissions";
import { But<PERSON> } from "@/components/ui/button";

interface ContactSubmissionsTableFloatingBarProps {
  table: Table<ContactSubmission>;
}

export function ContactSubmissionsTableFloatingBar({
  table,
}: ContactSubmissionsTableFloatingBarProps) {
  const selectedRows = table.getFilteredSelectedRowModel().rows;

  if (selectedRows.length === 0) return null;

  return (
    <div className="fixed bottom-4 left-1/2 z-50 flex w-fit -translate-x-1/2 gap-2 rounded-md border bg-card p-2 shadow-2xl">
      <div className="flex h-7 items-center rounded-md border border-dashed px-2.5 text-xs">
        {selectedRows.length} selected
      </div>
      <Button
        variant="outline"
        size="sm"
        className="h-7 border-dashed"
        onClick={() => {
          // Handle bulk delete
          console.log("Delete selected submissions:", selectedRows);
        }}
      >
        <Trash2 className="mr-2 size-4" />
        Delete
      </Button>
    </div>
  );
}
