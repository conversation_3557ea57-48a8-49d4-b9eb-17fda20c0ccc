"use client";

import * as React from "react";
import type {
  DataTableAdvancedFilter<PERSON>ield,
  DataTableFilterField,
} from "@/types";
import { useRouter } from "next/navigation";

import { type ContactSubmission } from "@/types/contact-submissions";
import { useDataTable } from "@/hooks/data-table/use-data-table";
import { DataTable } from "@/components/data-table/data-table";
import { DataTableToolbar } from "@/components/data-table/data-table-toolbar";
import { DataTableAdvancedToolbar } from "@/components/data-table/data-table-advanced-toolbar";

import { getContactSubmissions, getContactStatusCounts } from "../_lib/queries";
import { getColumns } from "./contact-submissions-table-columns";
import { ContactSubmissionsTableToolbarActions } from "./contact-submissions-table-toolbar-actions";
import { ContactSubmissionsTableFloatingBar } from "./contact-submissions-table-floating-bar";
import { useFeatureFlags } from "./feature-flags-provider";
import { type DataTableRowAction } from "@/types";
import { DeleteContactSubmissionsDialog } from "./delete-contact-submissions-dialog";

interface ContactSubmissionsTableProps {
  promises: Promise<
    [
      Awaited<ReturnType<typeof getContactSubmissions>>,
      Awaited<ReturnType<typeof getContactStatusCounts>>,
    ]
  >;
}

export function ContactSubmissionsTable({
  promises,
}: ContactSubmissionsTableProps) {
  const router = useRouter();
  const { featureFlags } = useFeatureFlags();
  const [{ data, pageCount }, statusCounts] = React.use(promises);
  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<ContactSubmission> | null>(null);

  const columns = React.useMemo(
    () => getColumns({ setRowAction }),
    [setRowAction]
  );

  const filterFields: DataTableFilterField<ContactSubmission>[] = [
    {
      // @ts-ignore
      id: "search",
      label: "Search",
      placeholder: "Search by name or email...",
    },
    {
      id: "status",
      label: "Status",
      options: [
        {
          label: "New",
          value: "new",
          count: statusCounts.new,
        },
        {
          label: "In Progress",
          value: "in_progress",
          count: statusCounts.in_progress,
        },
        {
          label: "Resolved",
          value: "resolved",
          count: statusCounts.resolved,
        },
        {
          label: "Closed",
          value: "closed",
          count: statusCounts.closed,
        },
      ],
    },
  ];

  const advancedFilterFields: DataTableAdvancedFilterField<ContactSubmission>[] =
    [
      {
        id: "firstName",
        label: "First Name",
        type: "text",
      },
      {
        id: "lastName",
        label: "Last Name",
        type: "text",
      },
      {
        id: "email",
        label: "Email",
        type: "text",
      },
      {
        id: "status",
        label: "Status",
        type: "multi-select",
        options: [
          {
            label: "New",
            value: "new",
            count: statusCounts.new,
          },
          {
            label: "In Progress",
            value: "in_progress",
            count: statusCounts.in_progress,
          },
          {
            label: "Resolved",
            value: "resolved",
            count: statusCounts.resolved,
          },
          {
            label: "Closed",
            value: "closed",
            count: statusCounts.closed,
          },
        ],
      },
      {
        id: "createdAt",
        label: "Created at",
        type: "date",
      },
    ];

  const enableAdvancedTable = featureFlags.includes("advancedTable");
  const enableFloatingBar = featureFlags.includes("floatingBar");

  const { table } = useDataTable({
    data,
    columns,
    pageCount,
    filterFields,
    enableAdvancedFilter: enableAdvancedTable,
    initialState: {
      sorting: [{ id: "createdAt", desc: true }],
      columnPinning: { right: ["actions"] },
    },
    getRowId: (originalRow) => originalRow._id,
    shallow: false,
    clearOnDefault: true,
  });

  return (
    <>
      <DataTable
        table={table}
        floatingBar={
          enableFloatingBar ? (
            <ContactSubmissionsTableFloatingBar table={table} />
          ) : null
        }
      >
        {enableAdvancedTable ? (
          <DataTableAdvancedToolbar
            table={table}
            filterFields={advancedFilterFields}
            shallow={false}
          >
            <ContactSubmissionsTableToolbarActions
              table={table}
              onRefresh={() => router.refresh()}
            />
          </DataTableAdvancedToolbar>
        ) : (
          <DataTableToolbar table={table} filterFields={filterFields}>
            <ContactSubmissionsTableToolbarActions
              table={table}
              onRefresh={() => router.refresh()}
            />
          </DataTableToolbar>
        )}
      </DataTable>
      <DeleteContactSubmissionsDialog
        open={rowAction?.type === "delete"}
        onOpenChange={() => setRowAction(null)}
        submissions={rowAction?.row ? [rowAction.row.original] : []}
        showTrigger={false}
        onSuccess={() => {
          rowAction?.row?.toggleSelected(false);
          router.refresh();
        }}
      />
    </>
  );
}
