import * as React from "react";
import Link from "next/link";
import { type SearchParams } from "@/types";

import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Skeleton } from "@/components/ui/skeleton";
import { DataTableSkeleton } from "@/components/data-table/data-table-skeleton";
import { DateRangePicker } from "@/components/date-range-picker";
import { Shell } from "@/components/shell";

import { PaymentsTable } from "./_components/payments-table";
import { getPayments, getPaymentStatusCounts } from "./_lib/queries";
import { searchParamsCache } from "./_lib/validations";
import { type Metadata } from "next";

export const metadata: Metadata = {
  title: "Payments",
  description: "Manage payments",
};

interface PaymentsPageProps {
  searchParams: Promise<SearchParams>;
}

export default async function PaymentsPage({
  searchParams,
}: PaymentsPageProps) {
  const search = searchParamsCache.parse(await searchParams);

  const promises = Promise.all([getPayments(search), getPaymentStatusCounts()]);

  const [paymentsResponse] = await promises;

  return (
    <ContentLayout title="Payments">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/admin">Dashboard</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Payments</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <Shell className="gap-2">
        <React.Suspense fallback={<Skeleton className="h-7 w-52" />}>
          <DateRangePicker
            triggerSize="sm"
            triggerClassName="ml-auto w-56 sm:w-60"
            align="end"
          />
        </React.Suspense>
        <React.Suspense
          fallback={
            <DataTableSkeleton
              columnCount={10}
              searchableColumnCount={2}
              filterableColumnCount={2}
              cellWidths={[
                "10rem",
                "15rem",
                "10rem",
                "10rem",
                "8rem",
                "10rem",
                "12rem",
                "12rem",
                "10rem",
                "8rem",
              ]}
              shrinkZero
            />
          }
        >
          <PaymentsTable
            data={paymentsResponse.data?.payments ?? []}
            pageCount={paymentsResponse.pageCount ?? 0}
          />
        </React.Suspense>
      </Shell>
    </ContentLayout>
  );
}
