"use client";

import * as React from "react";
import { type ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DataTable } from "@/components/data-table/data-table";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { DataTableToolbar } from "@/components/data-table/data-table-toolbar";
import { useDataTable } from "@/hooks/data-table/use-data-table";
import { Payment, StripePaymentStatus, PaymentMethod } from "@/types/payments";
import { formatDate } from "@/lib/data-table/utils";
import { MoreHorizontal, Eye, Receipt, RotateCcw } from "lucide-react";
import { type DataTableFilterField } from "@/types";

interface PaymentsTableProps {
  data: Payment[];
  pageCount: number;
}

export function PaymentsTable({ data, pageCount }: PaymentsTableProps) {
  const columns = React.useMemo<ColumnDef<Payment>[]>(
    () => [
      {
        id: "select",
        header: ({ table }) => (
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && "indeterminate")
            }
            onCheckedChange={(value) =>
              table.toggleAllPageRowsSelected(!!value)
            }
            aria-label="Select all"
            className="translate-y-0.5"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
            className="translate-y-0.5"
          />
        ),
        enableSorting: false,
        enableHiding: false,
      },
      {
        accessorKey: "stripePaymentIntentId",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Payment ID" />
        ),
        cell: ({ row }) => {
          const id = row.getValue("stripePaymentIntentId") as string;
          return (
            <div className="font-mono text-sm">
              {id ? id.substring(0, 20) + "..." : "-"}
            </div>
          );
        },
      },
      {
        accessorKey: "orderId",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Order ID" />
        ),
        cell: ({ row }) => (
          <div className="font-mono text-sm">{row.getValue("orderId")}</div>
        ),
      },
      {
        accessorKey: "amount",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Amount" />
        ),
        cell: ({ row }) => {
          const amount = row.getValue("amount") as number;
          const currency = row.original.currency || "USD";
          return (
            <div className="font-medium">
              {currency.toUpperCase()} {(amount / 100).toFixed(2)}
            </div>
          );
        },
      },
      {
        accessorKey: "status",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Status" />
        ),
        cell: ({ row }) => {
          const status = row.getValue("status") as StripePaymentStatus;
          return (
            <Badge
              variant={
                status === StripePaymentStatus.SUCCEEDED
                  ? "default"
                  : status === StripePaymentStatus.FAILED
                    ? "destructive"
                    : status === StripePaymentStatus.REFUNDED
                      ? "secondary"
                      : status === StripePaymentStatus.PROCESSING
                        ? "secondary"
                        : "outline"
              }
            >
              {status}
            </Badge>
          );
        },
      },
      {
        accessorKey: "paymentMethod",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Payment Method" />
        ),
        cell: ({ row }) => {
          const method = row.getValue("paymentMethod") as PaymentMethod;
          return method ? (
            <Badge variant="outline">{method.replace("_", " ")}</Badge>
          ) : (
            <span className="text-muted-foreground">-</span>
          );
        },
      },
      {
        accessorKey: "createdAt",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Created Date" />
        ),
        cell: ({ row }) => {
          const date = row.getValue("createdAt") as Date;
          return <div>{formatDate(date)}</div>;
        },
      },
      {
        accessorKey: "paidAt",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Paid Date" />
        ),
        cell: ({ row }) => {
          const date = row.getValue("paidAt") as Date;
          return <div>{date ? formatDate(date) : "-"}</div>;
        },
      },
      {
        accessorKey: "refundAmount",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Refund Amount" />
        ),
        cell: ({ row }) => {
          const refundAmount = row.getValue("refundAmount") as number;
          const currency = row.original.currency || "USD";
          return refundAmount ? (
            <div className="font-medium text-red-600">
              {currency.toUpperCase()} {(refundAmount / 100).toFixed(2)}
            </div>
          ) : (
            <span className="text-muted-foreground">-</span>
          );
        },
      },
      {
        id: "actions",
        cell: ({ row }) => {
          return (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  aria-label="Open menu"
                  variant="ghost"
                  className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
                >
                  <MoreHorizontal className="h-4 w-4" />
                  <span className="sr-only">Open menu</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-[160px]">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Receipt className="mr-2 h-4 w-4" />
                  View Receipt
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <RotateCcw className="mr-2 h-4 w-4" />
                  Refund Payment
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          );
        },
      },
    ],
    []
  );

  const filterFields: DataTableFilterField<Payment>[] = [
    {
      id: "stripePaymentIntentId",
      label: "Payment ID",
      placeholder: "Search payment IDs...",
    },
    {
      id: "orderId",
      label: "Order ID",
      placeholder: "Search order IDs...",
    },
    {
      id: "status",
      label: "Status",
      options: [
        { label: "Pending", value: StripePaymentStatus.PENDING },
        { label: "Processing", value: StripePaymentStatus.PROCESSING },
        { label: "Succeeded", value: StripePaymentStatus.SUCCEEDED },
        { label: "Failed", value: StripePaymentStatus.FAILED },
        { label: "Cancelled", value: StripePaymentStatus.CANCELLED },
        { label: "Refunded", value: StripePaymentStatus.REFUNDED },
        {
          label: "Partially Refunded",
          value: StripePaymentStatus.PARTIALLY_REFUNDED,
        },
      ],
    },
    {
      id: "paymentMethod",
      label: "Payment Method",
      options: [
        { label: "Card", value: PaymentMethod.CARD },
        { label: "Bank Transfer", value: PaymentMethod.BANK_TRANSFER },
        { label: "Wallet", value: PaymentMethod.WALLET },
      ],
    },
  ];

  const { table } = useDataTable({
    data,
    columns,
    pageCount,
    filterFields,
    enableAdvancedFilter: false,
    initialState: {
      sorting: [{ id: "createdAt", desc: true }],
      columnPinning: { right: ["actions"] },
    },
    getRowId: (originalRow) => originalRow._id,
    shallow: false,
    clearOnDefault: true,
  });

  return (
    <DataTable table={table}>
      <DataTableToolbar table={table} filterFields={filterFields} />
    </DataTable>
  );
}
