"use server";

import { type Payment } from "@/types/payments";
import { getAdminPayments } from "@/lib/payments";
import { ApiStatus } from "@/types/common";
import { type GetPaymentsSchema } from "./validations";

export async function getPayments(input: GetPaymentsSchema): Promise<{
  data: {
    payments: Payment[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
  pageCount: number;
}> {
  try {
    const params = {
      stripePaymentIntentId: input.stripePaymentIntentId,
      orderId: input.orderId,
      status: input.status.length > 0 ? input.status[0] : undefined,
      paymentMethod:
        input.paymentMethod.length > 0 ? input.paymentMethod[0] : undefined,
      limit: input.perPage || 10,
      page: input.page || 1,
      from: input.from || undefined,
      to: input.to || undefined,
      userId: input.userId || undefined,
      cartId: input.cartId || undefined,
    };

    const response = await getAdminPayments(params);

    if (response.status === ApiStatus.SUCCESS && response.data) {
      return {
        data: {
          payments: response.data.payments,
          total: response.data.total,
          page: response.data.page,
          limit: params.limit,
          totalPages: response.data.totalPages,
        },
        pageCount: response.data.totalPages,
      };
    }

    return {
      data: {
        payments: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      },
      pageCount: 0,
    };
  } catch (error) {
    console.error("Error fetching payments:", error);
    return {
      data: {
        payments: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      },
      pageCount: 0,
    };
  }
}

export async function getPaymentStatusCounts(): Promise<
  Array<{ status: string; count: number }>
> {
  try {
    // This would ideally come from a separate API endpoint
    // For now, we'll return an empty array and populate dynamically
    return [];
  } catch (error) {
    console.error("Error fetching payment status counts:", error);
    return [];
  }
}
