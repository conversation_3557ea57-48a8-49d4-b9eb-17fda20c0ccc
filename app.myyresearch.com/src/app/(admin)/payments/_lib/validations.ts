import {
  createSearchParamsCache,
  parseAsArrayOf,
  parseAsInteger,
  parseAsString,
  parseAsStringEnum,
} from "nuqs/server";
import * as z from "zod";

import {
  type Payment,
  StripePaymentStatus,
  PaymentMethod,
} from "@/types/payments";
import {
  getFiltersStateParser,
  getSortingStateParser,
} from "@/lib/data-table/parsers";

export const searchParamsCache = createSearchParamsCache({
  flags: parseAsArrayOf(z.enum(["advancedTable", "floatingBar"])).withDefault(
    []
  ),
  page: parseAsInteger.withDefault(1),
  perPage: parseAsInteger.withDefault(10),
  sort: getSortingStateParser<Payment>().withDefault([
    { id: "createdAt", desc: true },
  ]),
  stripePaymentIntentId: parseAsString.withDefault(""),
  orderId: parseAsString.withDefault(""),
  status: parseAsArrayOf(
    parseAsStringEnum(Object.values(StripePaymentStatus))
  ).withDefault([]),
  paymentMethod: parseAsArrayOf(
    parseAsStringEnum(Object.values(PaymentMethod))
  ).withDefault([]),
  from: parseAsString.withDefault(""),
  to: parseAsString.withDefault(""),
  userId: parseAsString.withDefault(""),
  cartId: parseAsString.withDefault(""),
  // Advanced filter
  filters: getFiltersStateParser().withDefault([]),
});

export type GetPaymentsSchema = Awaited<
  ReturnType<typeof searchParamsCache.parse>
>;

export const createPaymentIntentSchema = z.object({
  orderId: z.string().min(1, "Order ID is required"),
  cartId: z.string().min(1, "Cart ID is required"),
  metadata: z.record(z.any()).optional(),
});

export const confirmPaymentSchema = z.object({
  paymentIntentId: z.string().min(1, "Payment Intent ID is required"),
  metadata: z.record(z.any()).optional(),
});

export const refundPaymentSchema = z.object({
  amount: z.number().min(0).optional(),
  reason: z.string().optional(),
});

export type CreatePaymentIntentSchema = z.infer<
  typeof createPaymentIntentSchema
>;
export type ConfirmPaymentSchema = z.infer<typeof confirmPaymentSchema>;
export type RefundPaymentSchema = z.infer<typeof refundPaymentSchema>;
