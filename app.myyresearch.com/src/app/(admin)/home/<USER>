"use client";

import Link from "next/link";
import { Card } from "@/components/ui/card";
import {
  BookOpen,
  Users,
  Download,
  ShoppingCart,
  CreditCard,
  Star,
  Mail,
  Plus,
  Layout,
} from "lucide-react";
import { useAuth } from "@/contexts/auth-contexts";
import { useEffect, useState } from "react";
import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { cn } from "@/lib/utils";
import { Role } from "@/types/auth";

type NavigationItem = {
  title: string;
  href: string;
  icon: any;
  description: string;
  color: string;
  iconColor: string;
  roles?: Role[]; // Optional: specify which roles can see this item
};

function getGreeting() {
  const hour = new Date().getHours();
  if (hour >= 5 && hour < 12) return "Good Morning";
  if (hour >= 12 && hour < 17) return "Good Afternoon";
  if (hour >= 17 && hour < 21) return "Good Evening";
  return "Good Night";
}

// Helper function to check if a user role can access a navigation item
function canAccessNavigationItem(
  userRole: Role | undefined,
  allowedRoles?: Role[]
): boolean {
  if (!allowedRoles || allowedRoles.length === 0) {
    return true; // No restrictions
  }
  return userRole ? allowedRoles.includes(userRole) : false;
}

const navigationItems: NavigationItem[] = [
  {
    title: "Courses",
    href: "/courses",
    icon: BookOpen,
    description: "Manage all courses",
    color: "bg-blue-100 dark:bg-blue-950",
    iconColor: "text-blue-600 dark:text-blue-400",
    // Available to all roles
  },
  {
    title: "Add Course",
    href: "/courses/add",
    icon: Plus,
    description: "Create a new course",
    color: "bg-green-100 dark:bg-green-950",
    iconColor: "text-green-600 dark:text-green-400",
    // Available to all roles
  },
  {
    title: "Templates",
    href: "/templates",
    icon: Layout,
    description: "View all templates",
    color: "bg-purple-100 dark:bg-purple-950",
    iconColor: "text-purple-600 dark:text-purple-400",
    // Available to all roles
  },
  {
    title: "Add Template",
    href: "/templates/add",
    icon: Plus,
    description: "Create a new template",
    color: "bg-indigo-100 dark:bg-indigo-950",
    iconColor: "text-indigo-600 dark:text-indigo-400",
    // Available to all roles
  },
  {
    title: "Students",
    href: "/students",
    icon: Users,
    description: "Manage students",
    color: "bg-pink-100 dark:bg-pink-950",
    iconColor: "text-pink-600 dark:text-pink-400",
    roles: [Role.Admin], // Only ADMIN can see Students
  },
  {
    title: "Instructors",
    href: "/instructors",
    icon: Users,
    description: "Manage instructors",
    color: "bg-yellow-100 dark:bg-yellow-950",
    iconColor: "text-yellow-600 dark:text-yellow-400",
    roles: [Role.Admin], // Only ADMIN and INSTRUCTOR can see Instructors
  },
  {
    title: "Downloads",
    href: "/downloads",
    icon: Download,
    description: "Manage downloads",
    color: "bg-cyan-100 dark:bg-cyan-950",
    iconColor: "text-cyan-600 dark:text-cyan-400",
    roles: [Role.Admin, Role.Instructor], // STAFF cannot see Downloads
  },
  {
    title: "Enrollments",
    href: "/enrollments",
    icon: ShoppingCart,
    description: "View enrollments",
    color: "bg-teal-100 dark:bg-teal-950",
    iconColor: "text-teal-600 dark:text-teal-400",
    roles: [Role.Admin, Role.Instructor], // STAFF cannot see Enrollments
  },
  {
    title: "Payments",
    href: "/payments",
    icon: CreditCard,
    description: "Payment history",
    color: "bg-emerald-100 dark:bg-emerald-950",
    iconColor: "text-emerald-600 dark:text-emerald-400",
    roles: [Role.Admin], // Only ADMIN can see Payments
  },
  {
    title: "Reviews",
    href: "/reviews",
    icon: Star,
    description: "Course reviews",
    color: "bg-orange-100 dark:bg-orange-950",
    iconColor: "text-orange-600 dark:text-orange-400",
    roles: [Role.Admin, Role.Instructor], // STAFF cannot see Reviews
  },
  {
    title: "Users",
    href: "/users",
    icon: Users,
    description: "Manage users",
    color: "bg-slate-100 dark:bg-slate-950",
    iconColor: "text-slate-600 dark:text-slate-400",
    roles: [Role.Admin], // Only ADMIN can see Users
  },
  {
    title: "Contact Submissions",
    href: "/contact-submissions",
    icon: Mail,
    description: "Manage contact submissions",
    color: "bg-rose-100 dark:bg-rose-950",
    iconColor: "text-rose-600 dark:text-rose-400",
    roles: [Role.Admin], // Only ADMIN can see Contact Submissions
  },
];

const HomePage = () => {
  const { user } = useAuth();
  const [greeting, setGreeting] = useState(getGreeting());

  // Update greeting every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setGreeting(getGreeting());
    }, 60000); // Update every minute

    return () => clearInterval(timer);
  }, []);

  // Filter navigation items based on user role
  const filteredNavigationItems = navigationItems.filter((item) =>
    canAccessNavigationItem(user?.role, item.roles)
  );

  return (
    <ContentLayout title="Home">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">Home</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Dashboard</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="mt-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">
            {greeting}, {user?.username || "Admin"}
          </h1>
          <p className="text-muted-foreground">
            Welcome to your dashboard. Here's what you can manage today.
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {filteredNavigationItems.map((item) => {
            const Icon = item.icon;
            return (
              <Link href={item.href} key={item.href}>
                <Card
                  className={cn(
                    "p-4 transition-all duration-300 hover:scale-[1.02]",
                    item.color,
                    "border-none shadow-sm hover:shadow-md"
                  )}
                >
                  <div className="flex items-center space-x-4">
                    <div
                      className={cn(
                        "p-2 rounded-lg bg-white/80 dark:bg-black/20",
                        "shadow-sm"
                      )}
                    >
                      <Icon className={cn("w-6 h-6", item.iconColor)} />
                    </div>
                    <div>
                      <h2 className="font-semibold text-gray-900 dark:text-gray-100">
                        {item.title}
                      </h2>
                      <p className="text-sm text-gray-700 dark:text-gray-300">
                        {item.description}
                      </p>
                    </div>
                  </div>
                </Card>
              </Link>
            );
          })}
        </div>
      </div>
    </ContentLayout>
  );
};

export default HomePage;
