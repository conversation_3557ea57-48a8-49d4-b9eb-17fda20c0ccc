import { Metadata } from "next";
import { notFound } from "next/navigation";

import { getStudent } from "@/lib/students";
import PreviewStudentPageClient from "./_components/preview-student-page-client";

export const metadata: Metadata = {
  title: "Preview Student",
  description: "View student details in the MYY Research platform.",
};

interface PreviewStudentPageProps {
  params: Promise<{ id: string }>;
}

export default async function PreviewStudentPage({
  params,
}: PreviewStudentPageProps) {
  const result = await getStudent((await params).id);

  if (!result.data) {
    notFound();
  }

  return <PreviewStudentPageClient student={result.data} />;
}
