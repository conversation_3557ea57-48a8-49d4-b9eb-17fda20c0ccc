"use client";

import Link from "next/link";
import { format } from "date-fns";
import { Edit, FileText } from "lucide-react";
import { type Student } from "@/types/student";
import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

interface PreviewStudentPageClientProps {
  student: Student;
}

export default function PreviewStudentPageClient({
  student,
}: PreviewStudentPageClientProps) {
  return (
    <ContentLayout title="Student Details">
      <div className="max-w-5xl mx-auto space-y-8">
        <div className="flex flex-col space-y-1">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/students">Students</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href={`/students/preview/${student._id}`}>
                  Student Details
                </BreadcrumbLink>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>

          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-3xl font-bold tracking-tight">
                Student Details
              </h2>
              <p className="text-muted-foreground mt-2">
                View detailed information about the student.
              </p>
            </div>
            <Button asChild>
              <Link href={`/students/edit/${student._id}`}>
                <Edit className="mr-2 size-4" />
                Edit Student
              </Link>
            </Button>
          </div>
        </div>

        <div className="grid gap-6">
          {/* Personal Information */}
          <Card>
            <CardHeader>
              <CardTitle>Personal Information</CardTitle>
            </CardHeader>
            <CardContent className="grid gap-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
                <div>
                  <div className="text-sm font-medium text-muted-foreground">
                    Full Name
                  </div>
                  <div className="mt-1">{student.name}</div>
                </div>
                <div>
                  <div className="text-sm font-medium text-muted-foreground">
                    Email
                  </div>
                  <div className="mt-1">{student.email}</div>
                </div>
                <div>
                  <div className="text-sm font-medium text-muted-foreground">
                    ID/Passport
                  </div>
                  <div className="mt-1">{student.idPassport}</div>
                </div>
                <div>
                  <div className="text-sm font-medium text-muted-foreground">
                    Age
                  </div>
                  <div className="mt-1">{student.age} years</div>
                </div>
                <div>
                  <div className="text-sm font-medium text-muted-foreground">
                    Gender
                  </div>
                  <div className="mt-1 capitalize">{student.gender}</div>
                </div>
                <div>
                  <div className="text-sm font-medium text-muted-foreground">
                    Region
                  </div>
                  <div className="mt-1">{student.region}</div>
                </div>
                <div>
                  <div className="text-sm font-medium text-muted-foreground">
                    Occupation
                  </div>
                  <div className="mt-1">{student.occupation}</div>
                </div>
                <div>
                  <div className="text-sm font-medium text-muted-foreground">
                    Primary Reasons
                  </div>
                  <div className="mt-1">{student.primaryReasons}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tasks */}
          <Card>
            <CardHeader>
              <CardTitle>Tasks</CardTitle>
            </CardHeader>
            <CardContent className="grid gap-6">
              {student.tasks.map((task, index) => (
                <div key={index}>
                  {index > 0 && <Separator className="my-6" />}
                  <div className="grid gap-4">
                    <div className="flex items-center justify-between">
                      <Badge variant="outline" className="capitalize">
                        {task.type.replace("_", " ")}
                      </Badge>
                      <div className="text-sm text-muted-foreground">
                        Assigned: {format(new Date(task.assignedDate), "PPP")}
                      </div>
                    </div>

                    {task.type === "assignment_help" ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
                        <div>
                          <div className="text-sm font-medium text-muted-foreground">
                            Subjects
                          </div>
                          <div className="mt-1 flex flex-wrap gap-1">
                            {task.subjects.map((subject) => (
                              <Badge key={subject} variant="secondary">
                                {subject}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-muted-foreground">
                            Assignment Type
                          </div>
                          <div className="mt-1 capitalize">
                            {task.assignmentType.replace("_", " ")}
                          </div>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-muted-foreground">
                            Academic Level
                          </div>
                          <div className="mt-1 capitalize">
                            {task.academicLevel.replace("_", " ")}
                          </div>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-muted-foreground">
                            Preferred Format
                          </div>
                          <div className="mt-1 uppercase">
                            {task.preferredFormat}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
                        <div>
                          <div className="text-sm font-medium text-muted-foreground">
                            Research Topic
                          </div>
                          <div className="mt-1">{task.researchTopic}</div>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-muted-foreground">
                            Research Type
                          </div>
                          <div className="mt-1 capitalize">
                            {task.researchType.replace("_", " ")}
                          </div>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-muted-foreground">
                            Field of Study
                          </div>
                          <div className="mt-1">{task.fieldOfStudy}</div>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-muted-foreground">
                            Research Stage
                          </div>
                          <div className="mt-1 capitalize">
                            {task.researchStage.replace("_", " ")}
                          </div>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-muted-foreground">
                            Required Support
                          </div>
                          <div className="mt-1 capitalize">
                            {task.requiredSupport.replace("_", " ")}
                          </div>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-muted-foreground">
                            Tools Used
                          </div>
                          <div className="mt-1 flex flex-wrap gap-1">
                            {task.toolsUsed.map((tool) => (
                              <Badge key={tool} variant="secondary">
                                {tool.replace("_", " ")}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      </div>
    </ContentLayout>
  );
}
