"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { createStudent } from "@/lib/students";
import { ContentLayout } from "@/components/admin-panel/content-layout";
import { StudentForm, formSchema } from "../../_components/student-form";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Card, CardContent } from "@/components/ui/card";
import { z } from "zod";

export default function AddStudentPageClient() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();
  const { toast } = useToast();

  const handleSubmit = async (data: z.infer<typeof formSchema>) => {
    setIsSubmitting(true);
    try {
      await createStudent(data);
      toast({
        title: "Success",
        description: "Student created successfully",
      });
      router.push("/students");
    } catch (error) {
      console.error(error);
      toast({
        title: "Error",
        description: "Something went wrong while creating the student",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ContentLayout title="Add Student">
      <div className="max-w-5xl mx-auto space-y-8">
        <div className="flex flex-col space-y-1">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/students">Students</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href="/students/add">
                  Add Student
                </BreadcrumbLink>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>

          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-3xl font-bold tracking-tight">
                Add New Student
              </h2>
              <p className="text-muted-foreground mt-2">
                Fill in the student's basic details and add their tasks below.
              </p>
            </div>
          </div>
        </div>

        <Card>
          <CardContent className="p-6">
            <StudentForm onSubmit={handleSubmit} isSubmitting={isSubmitting} />
          </CardContent>
        </Card>
      </div>
    </ContentLayout>
  );
}
