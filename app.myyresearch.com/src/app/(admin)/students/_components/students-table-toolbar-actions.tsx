"use client";

import { type Table } from "@tanstack/react-table";
import { Download } from "lucide-react";

import { type Student } from "@/types/student";
import { exportTableToCSV } from "@/lib/data-table/export";
import { Button } from "@/components/ui/button";

import { DeleteStudentsDialog } from "./delete-students-dialog";

interface StudentsTableToolbarActionsProps {
  table: Table<Student>;
  onRefresh?: () => void;
}

export function StudentsTableToolbarActions({
  table,
  onRefresh,
}: StudentsTableToolbarActionsProps) {
  return (
    <div className="flex items-center gap-2">
      {table.getFilteredSelectedRowModel().rows.length > 0 ? (
        <DeleteStudentsDialog
          students={table
            .getFilteredSelectedRowModel()
            .rows.map((row) => row.original)}
          onSuccess={() => {
            table.toggleAllRowsSelected(false);
            onRefresh?.();
          }}
        />
      ) : null}
      <Button
        variant="outline"
        size="sm"
        onClick={() =>
          exportTableToCSV(table, {
            filename: "students",
            excludeColumns: ["select", "actions"],
          })
        }
        className="gap-2"
      >
        <Download className="size-4" aria-hidden="true" />
        Export
      </Button>
    </div>
  );
}
