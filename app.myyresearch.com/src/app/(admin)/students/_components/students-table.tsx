"use client";

import * as React from "react";
import type {
  DataTableAdvancedFilterField,
  DataTableFilterField,
  DataTableRowAction,
} from "@/types";

import { type Student } from "@/types/student";
import { toSentenceCase } from "@/lib/data-table/utils";
import { useDataTable } from "@/hooks/data-table/use-data-table";
import { DataTable } from "@/components/data-table/data-table";
import { DataTableAdvancedToolbar } from "@/components/data-table/data-table-advanced-toolbar";
import { DataTableToolbar } from "@/components/data-table/data-table-toolbar";

import { getStudents } from "@/lib/students";
import { DeleteStudentsDialog } from "./delete-students-dialog";
import { useFeatureFlags } from "./feature-flags-provider";
import { getColumns } from "./students-table-columns";
import { StudentsTableFloatingBar } from "./students-table-floating-bar";
import { StudentsTableToolbarActions } from "./students-table-toolbar-actions";
import { UpdateStudentSheet } from "./update-student-sheet";
import { useRouter } from "next/navigation";

interface StudentsTableProps {
  promises: Promise<[Awaited<ReturnType<typeof getStudents>>]>;
}

export function StudentsTable({ promises }: StudentsTableProps) {
  const router = useRouter();
  const { featureFlags } = useFeatureFlags();

  const [response] = React.use(promises);
  const data = response.data?.students ?? [];
  const pageCount = response.data?.totalPages ?? 0;

  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<Student> | null>(null);

  const columns = React.useMemo(
    () => getColumns({ setRowAction }),
    [setRowAction]
  );

  const filterFields: DataTableFilterField<Student>[] = [
    {
      id: "email",
      label: "Email",
      placeholder: "Filter emails...",
    },
    {
      id: "name",
      label: "Name",
      placeholder: "Filter names...",
    },
    {
      id: "region",
      label: "Region",
      placeholder: "Filter regions...",
    },
  ];

  const advancedFilterFields: DataTableAdvancedFilterField<Student>[] = [
    {
      id: "email",
      label: "Email",
      type: "text",
    },
    {
      id: "name",
      label: "Name",
      type: "text",
    },
    {
      id: "region",
      label: "Region",
      type: "text",
    },
    {
      id: "createdAt",
      label: "Created at",
      type: "date",
    },
  ];

  const enableAdvancedTable = featureFlags.includes("advancedTable");
  const enableFloatingBar = featureFlags.includes("floatingBar");

  const { table } = useDataTable({
    data,
    columns,
    pageCount,
    filterFields,
    enableAdvancedFilter: enableAdvancedTable,
    initialState: {
      sorting: [{ id: "createdAt", desc: true }],
      columnPinning: { right: ["actions"] },
    },
    getRowId: (originalRow) => originalRow._id,
    shallow: false,
    clearOnDefault: true,
  });

  return (
    <>
      <DataTable
        table={table}
        floatingBar={
          enableFloatingBar ? <StudentsTableFloatingBar table={table} /> : null
        }
      >
        {enableAdvancedTable ? (
          <DataTableAdvancedToolbar
            table={table}
            filterFields={advancedFilterFields}
            shallow={false}
          >
            <StudentsTableToolbarActions
              table={table}
              onRefresh={() => router.refresh()}
            />
          </DataTableAdvancedToolbar>
        ) : (
          <DataTableToolbar table={table} filterFields={filterFields}>
            <StudentsTableToolbarActions
              table={table}
              onRefresh={() => router.refresh()}
            />
          </DataTableToolbar>
        )}
      </DataTable>
      <UpdateStudentSheet
        open={rowAction?.type === "update"}
        onOpenChange={() => setRowAction(null)}
        student={rowAction?.row.original ?? null}
      />
      <DeleteStudentsDialog
        open={rowAction?.type === "delete"}
        onOpenChange={() => setRowAction(null)}
        students={rowAction?.row.original ? [rowAction?.row.original] : []}
        showTrigger={false}
        onSuccess={() => rowAction?.row.toggleSelected(false)}
      />
    </>
  );
}
