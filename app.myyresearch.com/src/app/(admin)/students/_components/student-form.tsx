"use client";

import * as React from "react";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { format } from "date-fns";
import { Calendar as CalendarIcon, Loader2, SaveIcon } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import {
  TaskType,
  Gender,
  AssignmentType,
  AcademicLevel,
  FileFormat,
  ResearchType,
  ResearchStage,
  ResearchSupport,
  ResearchTool,
  Student,
} from "@/types/student";
import { getErrorMessage } from "@/lib/data-table/handle-error";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { MultiSelect } from "@/components/ui/multi-select";
import { useToast } from "@/hooks/use-toast";
import { FancyMultiSelect, Option } from "@/components/ui/fancy-multi-select";

const assignmentTaskSchema = z.object({
  type: z.literal(TaskType.ASSIGNMENT_HELP),
  subjects: z.array(z.string()).min(1, "At least one subject is required"),
  assignmentType: z.nativeEnum(AssignmentType),
  academicLevel: z.nativeEnum(AcademicLevel),
  preferredFormat: z.nativeEnum(FileFormat),
  assignedDate: z.date({
    required_error: "Assigned date is required",
  }),
});

const researchTaskSchema = z.object({
  type: z.literal(TaskType.RESEARCH_HELP),
  researchTopic: z.string().min(1, "Research topic is required"),
  researchType: z.nativeEnum(ResearchType),
  fieldOfStudy: z.string().min(1, "Field of study is required"),
  researchStage: z.nativeEnum(ResearchStage),
  requiredSupport: z.nativeEnum(ResearchSupport),
  toolsUsed: z
    .array(z.nativeEnum(ResearchTool))
    .min(1, "At least one tool is required"),
  assignedDate: z.date({
    required_error: "Assigned date is required",
  }),
});

export const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  idPassport: z.string().min(1, "ID/Passport is required"),
  age: z.coerce
    .number()
    .min(16, "Age must be at least 16")
    .max(100, "Age must be at most 100"),
  gender: z.nativeEnum(Gender),
  region: z.string().min(1, "Region is required"),
  occupation: z.string().min(1, "Occupation is required"),
  primaryReasons: z.string().min(1, "Primary reasons are required"),
  email: z.string().email("Invalid email address"),
  tasks: z
    .array(
      z.discriminatedUnion("type", [assignmentTaskSchema, researchTaskSchema])
    )
    .min(1, "At least one task is required"),
});

interface StudentFormProps {
  initialData?: Student;
  onSubmit: (data: z.infer<typeof formSchema>) => Promise<void>;
  isSubmitting?: boolean;
}

const subjectOptions: Option[] = [
  { label: "Mathematics", value: "mathematics" },
  { label: "Physics", value: "physics" },
  { label: "Chemistry", value: "chemistry" },
  { label: "Biology", value: "biology" },
  { label: "Computer Science", value: "computer_science" },
  { label: "Engineering", value: "engineering" },
  { label: "Literature", value: "literature" },
  { label: "History", value: "history" },
  { label: "Economics", value: "economics" },
  { label: "Psychology", value: "psychology" },
];

const getToolOptions = () =>
  Object.values(ResearchTool).map((tool) => ({
    label: tool
      .replace(/_/g, " ")
      .replace(
        /\w\S*/g,
        (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
      ),
    value: tool,
  }));

export function StudentForm({
  initialData,
  onSubmit,
  isSubmitting = false,
}: StudentFormProps) {
  const [currentTaskType, setCurrentTaskType] = React.useState<TaskType | null>(
    null
  );
  const [taskToRemove, setTaskToRemove] = React.useState<number | null>(null);
  const { toast } = useToast();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: initialData || {
      name: "",
      idPassport: "",
      age: 16,
      gender: Gender.MALE,
      region: "",
      occupation: "",
      primaryReasons: "",
      email: "",
      tasks: [],
    },
  });

  const addTask = () => {
    if (!currentTaskType) return;

    const currentTasks = form.getValues("tasks") || [];

    if (currentTaskType === TaskType.ASSIGNMENT_HELP) {
      currentTasks.push({
        type: TaskType.ASSIGNMENT_HELP,
        subjects: [],
        assignmentType: AssignmentType.ESSAY,
        academicLevel: AcademicLevel.UNDERGRADUATE,
        preferredFormat: FileFormat.PDF,
        assignedDate: new Date(),
      });
    } else {
      currentTasks.push({
        type: TaskType.RESEARCH_HELP,
        researchTopic: "",
        researchType: ResearchType.QUALITATIVE,
        fieldOfStudy: "",
        researchStage: ResearchStage.PROPOSAL,
        requiredSupport: ResearchSupport.METHODOLOGY,
        toolsUsed: [],
        assignedDate: new Date(),
      });
    }

    form.setValue("tasks", currentTasks);
  };

  const removeTask = (index: number) => {
    const currentTasks = form.getValues("tasks");
    currentTasks.splice(index, 1);
    form.setValue("tasks", currentTasks);
    setTaskToRemove(null);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-12">
        {/* Personal Information Section */}
        <div className="space-y-8">
          <div className="border-b pb-4">
            <h3 className="text-xl font-semibold">Personal Information</h3>
            <p className="text-sm text-muted-foreground mt-2">
              Enter the student's basic details and contact information.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6 bg-muted/5 rounded-lg p-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">Full Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="John Doe"
                      {...field}
                      className="bg-background"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">Email Address</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="<EMAIL>"
                      {...field}
                      className="bg-background"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="idPassport"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">
                    ID/Passport Number
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="A123456789"
                      {...field}
                      className="bg-background"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="age"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">Age</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} className="bg-background" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="gender"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">Gender</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="bg-background">
                        <SelectValue placeholder="Select gender" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.values(Gender).map((gender) => (
                        <SelectItem key={gender} value={gender}>
                          {gender}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="region"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">Region</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="North America"
                      {...field}
                      className="bg-background"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="occupation"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">Occupation</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Student"
                      {...field}
                      className="bg-background"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="primaryReasons"
              render={({ field }) => (
                <FormItem className="col-span-1 md:col-span-2">
                  <FormLabel className="font-medium">
                    Primary Reasons for Support
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Please describe the main reasons for seeking support..."
                      className="resize-none bg-background min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Tasks Section */}
        <div className="space-y-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between border-b pb-4">
            <div>
              <h3 className="text-xl font-semibold">Tasks</h3>
              <p className="text-sm text-muted-foreground mt-2">
                Add and manage student tasks.
              </p>
            </div>
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 mt-4 sm:mt-0">
              <Select
                onValueChange={(value) => setCurrentTaskType(value as TaskType)}
              >
                <SelectTrigger className="w-full sm:w-[200px]">
                  <SelectValue placeholder="Select task type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={TaskType.ASSIGNMENT_HELP}>
                    Assignment Help
                  </SelectItem>
                  <SelectItem value={TaskType.RESEARCH_HELP}>
                    Research Help
                  </SelectItem>
                </SelectContent>
              </Select>
              <Button
                type="button"
                variant="secondary"
                onClick={addTask}
                disabled={!currentTaskType}
                className="w-full sm:w-auto"
              >
                Add Task
              </Button>
            </div>
          </div>

          {/* Task List */}
          <div className="space-y-6">
            {form.watch("tasks").map((task, index) => (
              <div
                key={index}
                className="border rounded-lg p-6 space-y-6 bg-muted/5 hover:bg-muted/10 transition-colors"
              >
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                  <div className="space-y-1">
                    <h4 className="text-lg font-semibold">
                      {task.type === TaskType.ASSIGNMENT_HELP
                        ? "Assignment Help"
                        : "Research Help"}
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      Task #{index + 1}
                    </p>
                  </div>
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    onClick={() => setTaskToRemove(index)}
                    className="w-full sm:w-auto"
                  >
                    Remove Task
                  </Button>
                </div>

                {task.type === TaskType.ASSIGNMENT_HELP ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name={`tasks.${index}.subjects`}
                      render={({ field }) => (
                        <FormItem className="col-span-2">
                          <FormLabel>Subjects</FormLabel>
                          <FormControl>
                            <FancyMultiSelect
                              placeholder="Select subjects"
                              options={subjectOptions}
                              selected={
                                Array.isArray(field.value)
                                  ? field.value.map((value: string) => ({
                                      label:
                                        subjectOptions.find(
                                          (opt) => opt.value === value
                                        )?.label || value,
                                      value,
                                    }))
                                  : []
                              }
                              onChange={(selected) => {
                                field.onChange(
                                  selected.map((opt) => opt.value)
                                );
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name={`tasks.${index}.assignmentType`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Assignment Type</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {Object.values(AssignmentType).map((type) => (
                                <SelectItem key={type} value={type}>
                                  {type}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name={`tasks.${index}.academicLevel`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Academic Level</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select level" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {Object.values(AcademicLevel).map((level) => (
                                <SelectItem key={level} value={level}>
                                  {level}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name={`tasks.${index}.preferredFormat`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Preferred Format</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select format" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {Object.values(FileFormat).map((format) => (
                                <SelectItem key={format} value={format}>
                                  {format}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name={`tasks.${index}.assignedDate`}
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Assigned Date</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant={"outline"}
                                  className={cn(
                                    "w-full pl-3 text-left font-normal",
                                    !field.value && "text-muted-foreground"
                                  )}
                                >
                                  {field.value ? (
                                    format(field.value, "PPP")
                                  ) : (
                                    <span>Pick a date</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent
                              className="w-auto p-0"
                              align="start"
                            >
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) =>
                                  date < new Date("1900-01-01")
                                }
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name={`tasks.${index}.researchTopic`}
                      render={({ field }) => (
                        <FormItem className="col-span-2">
                          <FormLabel>Research Topic</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name={`tasks.${index}.researchType`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Research Type</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {Object.values(ResearchType).map((type) => (
                                <SelectItem key={type} value={type}>
                                  {type}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name={`tasks.${index}.fieldOfStudy`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Field of Study</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name={`tasks.${index}.researchStage`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Research Stage</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select stage" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {Object.values(ResearchStage).map((stage) => (
                                <SelectItem key={stage} value={stage}>
                                  {stage}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name={`tasks.${index}.requiredSupport`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Required Support</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select support" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {Object.values(ResearchSupport).map((support) => (
                                <SelectItem key={support} value={support}>
                                  {support}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name={`tasks.${index}.toolsUsed`}
                      render={({ field }) => (
                        <FormItem className="col-span-2">
                          <FormLabel>Tools Used</FormLabel>
                          <FormControl>
                            <FancyMultiSelect
                              placeholder="Select tools"
                              options={getToolOptions()}
                              selected={
                                Array.isArray(field.value)
                                  ? field.value.map((value: ResearchTool) => {
                                      const option = getToolOptions().find(
                                        (opt) => opt.value === value
                                      );
                                      return {
                                        label: option?.label || String(value),
                                        value,
                                      };
                                    })
                                  : []
                              }
                              onChange={(selected) => {
                                field.onChange(
                                  selected.map(
                                    (opt) => opt.value as ResearchTool
                                  )
                                );
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name={`tasks.${index}.assignedDate`}
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Assigned Date</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant={"outline"}
                                  className={cn(
                                    "w-full pl-3 text-left font-normal",
                                    !field.value && "text-muted-foreground"
                                  )}
                                >
                                  {field.value ? (
                                    format(field.value, "PPP")
                                  ) : (
                                    <span>Pick a date</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent
                              className="w-auto p-0"
                              align="start"
                            >
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) =>
                                  date < new Date("1900-01-01")
                                }
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                )}
              </div>
            ))}

            {form.watch("tasks").length === 0 && (
              <div className="text-center py-8 border-2 border-dashed rounded-lg">
                <p className="text-muted-foreground">
                  No tasks added yet. Select a task type and click "Add Task" to
                  get started.
                </p>
              </div>
            )}
          </div>
        </div>

        <div className="flex justify-end pt-4 border-t">
          <Button
            type="submit"
            size="lg"
            className="gap-2 min-w-[150px]"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Saving...</span>
              </>
            ) : (
              <>
                <SaveIcon className="h-4 w-4" />
                <span>Save Changes</span>
              </>
            )}
          </Button>
        </div>
      </form>

      <Dialog
        open={taskToRemove !== null}
        onOpenChange={() => setTaskToRemove(null)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Remove Task</DialogTitle>
            <DialogDescription>
              Are you sure you want to remove this task? This action cannot be
              undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="gap-2">
            <Button variant="outline" onClick={() => setTaskToRemove(null)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => {
                if (taskToRemove !== null) {
                  removeTask(taskToRemove);
                }
              }}
            >
              Remove
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Form>
  );
}
