"use client";

import * as React from "react";
import { useQueryState } from "nuqs";

import { dataTableConfig, type DataTableConfig } from "@/config/data-table";
import { cn } from "@/lib/utils";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

type FeatureFlagValue = DataTableConfig["featureFlags"][number]["value"];

interface FeatureFlagsProviderProps {
  children: React.ReactNode;
}

interface FeatureFlagsContextValue {
  featureFlags: FeatureFlagValue[];
}

const FeatureFlagsContext = React.createContext<FeatureFlagsContextValue>({
  featureFlags: [],
});

export function FeatureFlagsProvider({ children }: FeatureFlagsProviderProps) {
  const [rawFeatureFlags = [], setFeatureFlags] = useQueryState<string[]>(
    "featureFlags",
    {
      parse: (value) => value?.split(",").filter(Boolean) ?? [],
      serialize: (value) => value?.join(",") ?? "",
    }
  );

  const validFeatureFlags = (rawFeatureFlags ?? []).filter(
    (flag): flag is FeatureFlagValue =>
      dataTableConfig.featureFlags.some(({ value }) => value === flag)
  );

  return (
    <FeatureFlagsContext.Provider value={{ featureFlags: validFeatureFlags }}>
      <div className="flex flex-col gap-4">
        <div className="flex items-center gap-2">
          <ToggleGroup
            type="multiple"
            value={validFeatureFlags}
            onValueChange={setFeatureFlags}
            className="flex-wrap"
          >
            {dataTableConfig.featureFlags.map(
              ({ label, value, icon: Icon }) => (
                <Tooltip key={value}>
                  <TooltipTrigger asChild>
                    <ToggleGroupItem
                      value={value}
                      size="sm"
                      className={cn(
                        "gap-2 data-[state=on]:bg-primary data-[state=on]:text-primary-foreground"
                      )}
                    >
                      <Icon className="size-4" aria-hidden="true" />
                      {label}
                    </ToggleGroupItem>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Toggle {label.toLowerCase()}</p>
                  </TooltipContent>
                </Tooltip>
              )
            )}
          </ToggleGroup>
        </div>
        {children}
      </div>
    </FeatureFlagsContext.Provider>
  );
}

export function useFeatureFlags() {
  const context = React.useContext(FeatureFlagsContext);

  if (!context) {
    throw new Error(
      "useFeatureFlags must be used within a FeatureFlagsProvider"
    );
  }

  return context;
}
