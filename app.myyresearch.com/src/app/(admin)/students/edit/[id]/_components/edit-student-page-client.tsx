"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { updateStudent } from "@/lib/students";
import { type Student } from "@/types/student";
import { ContentLayout } from "@/components/admin-panel/content-layout";
import { StudentForm, formSchema } from "../../../_components/student-form";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Card, CardContent } from "@/components/ui/card";
import { z } from "zod";

interface EditStudentPageClientProps {
  student: Student;
}

export default function EditStudentPageClient({
  student,
}: EditStudentPageClientProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();
  const { toast } = useToast();

  const handleSubmit = async (data: z.infer<typeof formSchema>) => {
    setIsSubmitting(true);
    try {
      const result = await updateStudent(student._id, data);
      if (result.status === "FAIL") {
        throw new Error(result.message || "Failed to update student");
      }
      toast({
        title: "Success",
        description: "Student updated successfully",
      });
      router.push("/students");
    } catch (error) {
      console.error(error);
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Something went wrong while updating the student",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ContentLayout title="Edit Student">
      <div className="max-w-5xl mx-auto space-y-8">
        <div className="flex flex-col space-y-1">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/students">Students</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href={`/students/edit/${student._id}`}>
                  Edit Student
                </BreadcrumbLink>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>

          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-3xl font-bold tracking-tight">
                Edit Student
              </h2>
              <p className="text-muted-foreground mt-2">
                Update the student's details below.
              </p>
            </div>
          </div>
        </div>

        <Card>
          <CardContent className="p-6">
            <StudentForm
              onSubmit={handleSubmit}
              isSubmitting={isSubmitting}
              initialData={student}
            />
          </CardContent>
        </Card>
      </div>
    </ContentLayout>
  );
}
