import { Metadata } from "next";
import { notFound } from "next/navigation";

import { getStudent } from "@/lib/students";
import EditStudentPageClient from "./_components/edit-student-page-client";

export const metadata: Metadata = {
  title: "Edit Student",
  description: "Edit student details in the MYY Research platform.",
};

interface EditStudentPageProps {
  params: Promise<{ id: string }>;
}

export default async function EditStudentPage({
  params,
}: EditStudentPageProps) {
  const result = await getStudent((await params).id);

  if (!result.data) {
    notFound();
  }

  return <EditStudentPageClient student={result.data} />;
}
