import { Suspense } from "react";
import Link from "next/link";
import { type SearchParams } from "@/types";

import { getStudents } from "@/lib/students";
import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { DataTableSkeleton } from "@/components/data-table/data-table-skeleton";
import { Shell } from "@/components/shell";
import { Skeleton } from "@/components/ui/skeleton";
import { DateRangePicker } from "@/components/date-range-picker";

import { StudentsTable } from "./_components/students-table";
import { FeatureFlagsProvider } from "./_components/feature-flags-provider";
import { searchParamsCache } from "./_lib/validations";
import { getValidFilters } from "@/lib/data-table/data-table";

export const metadata = {
  title: "Students",
  description: "Manage your students",
};

interface StudentsPageProps {
  searchParams: Promise<SearchParams>;
}

export default async function StudentsPage({
  searchParams,
}: StudentsPageProps) {
  const search = searchParamsCache.parse(await searchParams);
  const validFilters = search.filters
    ? getValidFilters(JSON.parse(search.filters))
    : [];
  const validSort = search.sort ? JSON.parse(search.sort) : undefined;

  const promises = Promise.all([
    getStudents({
      ...search,
      filters: validFilters,
      sort: validSort,
    }),
  ]);

  return (
    <ContentLayout title="Students">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">Home</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Students</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <Shell className="gap-2">
        <FeatureFlagsProvider>
          <Suspense fallback={<DataTableSkeleton columnCount={6} />}>
            <StudentsTable promises={promises} />
          </Suspense>
        </FeatureFlagsProvider>
      </Shell>
    </ContentLayout>
  );
}
