"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useRouter, useSearchParams } from "next/navigation";
import { Loader2 } from "lucide-react";
import { login, createSession } from "@/lib/authenticaton";
import { loginFormSchema } from "@/schema/auth";
import { ApiStatus } from "@/types/common";
import TwoFactorForm from "./two-factor-form";
import { useToast } from "@/hooks/use-toast";
import { Logo } from "@/components/ui/logo";
import { useAuth } from "@/contexts/auth-contexts";
import { PasswordInput } from "@/components/ui/password-input";

export default function LoginForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const [userId, setUserId] = useState<string | null>(null);
  const [isAutoAuthenticating, setIsAutoAuthenticating] = useState(false);
  const { toast } = useToast();
  const { login: authLogin } = useAuth();
  const searchParams = useSearchParams();
  const form = useForm<z.infer<typeof loginFormSchema>>({
    resolver: zodResolver(loginFormSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const router = useRouter();

  // Auto-authentication with tokens from query parameters
  useEffect(() => {
    const handleAutoAuthentication = async () => {
      const accessToken = searchParams.get("accessToken");
      const refreshToken = searchParams.get("refreshToken");

      // Security validation: Check if tokens exist and are not empty
      if (
        accessToken &&
        refreshToken &&
        accessToken.trim() !== "" &&
        refreshToken.trim() !== ""
      ) {
        // Security validation: Check token length to prevent potential attacks
        if (accessToken.length > 2048 || refreshToken.length > 2048) {
          console.error("Token length exceeds security limits");
          toast({
            variant: "destructive",
            description:
              "Invalid authentication tokens. Please login manually.",
          });
          return;
        }

        setIsAutoAuthenticating(true);

        try {
          let decodedAccessToken: string;
          let decodedRefreshToken: string;

          // Safely decode tokens with error handling
          try {
            decodedAccessToken = decodeURIComponent(accessToken);
            decodedRefreshToken = decodeURIComponent(refreshToken);
          } catch (decodeError) {
            throw new Error("Invalid token encoding");
          }

          // Validate decoded tokens are not empty
          if (
            decodedAccessToken.trim() === "" ||
            decodedRefreshToken.trim() === ""
          ) {
            throw new Error("Decoded tokens are empty");
          }

          // Create session with the provided tokens
          await createSession({
            accessToken: decodedAccessToken,
            refreshToken: decodedRefreshToken,
          });

          // Get user profile to complete authentication
          const { getProfile } = await import("@/lib/authenticaton");
          const profileResponse = await getProfile();

          if (profileResponse.status === ApiStatus.SUCCESS) {
            // Verify user has appropriate role for admin portal
            const userRole = profileResponse.data.role;
            if (userRole !== "instructor" && userRole !== "admin") {
              throw new Error("Insufficient permissions for admin portal");
            }

            await authLogin(profileResponse.data, {
              accessToken: decodedAccessToken,
              refreshToken: decodedRefreshToken,
            });

            toast({
              description: "You have been automatically logged in.",
            });

            // Clear URL parameters for security
            const url = new URL(window.location.href);
            url.searchParams.delete("accessToken");
            url.searchParams.delete("refreshToken");
            window.history.replaceState({}, "", url.toString());

            // Redirect to admin dashboard
            router.push("/");
          } else {
            throw new Error(
              `Failed to get user profile: ${profileResponse.message || "Unknown error"}`
            );
          }
        } catch (error) {
          console.error("Auto-authentication failed:", error);

          let errorMessage =
            "Auto-authentication failed. Please login manually.";
          if (error instanceof Error) {
            if (
              error.message.includes("Invalid token") ||
              error.message.includes("Decoded tokens")
            ) {
              errorMessage =
                "Invalid authentication tokens. Please login manually.";
            } else if (error.message.includes("Insufficient permissions")) {
              errorMessage =
                "You don't have permission to access the admin portal.";
            }
          }

          toast({
            variant: "destructive",
            description: errorMessage,
          });

          // Clear the URL parameters to show the login form
          const url = new URL(window.location.href);
          url.searchParams.delete("accessToken");
          url.searchParams.delete("refreshToken");
          window.history.replaceState({}, "", url.toString());
        } finally {
          setIsAutoAuthenticating(false);
        }
      }
    };

    handleAutoAuthentication();
  }, [searchParams, authLogin, router, toast]);

  const onSubmit = async (values: z.infer<typeof loginFormSchema>) => {
    try {
      const res = await login({
        identifier: values.email,
        password: values.password,
      });

      if (res.status === ApiStatus.SUCCESS) {
        if (res.data.requires2FA) {
          setUserId(res.data.userId || null);
        } else {
          await authLogin(res.data.user, res.data.tokens);

          toast({
            description: "You have successfully Logged in.",
          });
          router.push("/");
        }
      } else {
        toast({
          variant: "destructive",
          description: res.message || "An error occurred.",
        });
      }
    } catch (error) {
      console.error("Login error:", error);
      toast({
        variant: "destructive",
        description: "Failed to login. Please try again later.",
      });
    }
  };

  const handle2FAError = (error: string) => {
    toast({
      variant: "destructive",
      description: error,
    });
  };

  if (userId) {
    return <TwoFactorForm userId={userId} onError={handle2FAError} />;
  }

  // Show loading state during auto-authentication
  if (isAutoAuthenticating) {
    return (
      <div className="p-6 md:p-8">
        <div className="flex flex-col gap-6">
          <div className="flex flex-col items-center text-center">
            <Logo />
            <p className="text-balance text-muted-foreground mt-3">
              Authenticating...
            </p>
          </div>
          <div className="flex justify-center">
            <Loader2 className="size-8 animate-spin" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="p-6 md:p-8">
      <div className="flex flex-col gap-6">
        <div className="flex flex-col items-center text-center">
          <Logo />
          <p className="text-balance text-muted-foreground mt-3">
            Login to your Account
          </p>
        </div>
        <div className="grid gap-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            {...form.register("email")}
          />
          {form.formState.errors.email && (
            <p className="text-sm text-red-500">
              {form.formState.errors.email.message}
            </p>
          )}
        </div>
        <div className="grid gap-2">
          <div className="flex items-center">
            <Label htmlFor="password">Password</Label>
            {/* <a
              href="/forgot-password"
              className="ml-auto text-sm underline-offset-2 hover:underline"
            >
              Forgot your password?
            </a> */}
          </div>
          <PasswordInput
            id="password"
            disabled={form.formState.isSubmitting}
            {...form.register("password")}
          />
          {form.formState.errors.password && (
            <p className="text-sm text-red-500">
              {form.formState.errors.password.message}
            </p>
          )}
        </div>
        <Button
          type="submit"
          className="w-full"
          disabled={form.formState.isSubmitting}
        >
          {form.formState.isSubmitting ? (
            <Loader2 className="size-6 animate-spin" />
          ) : (
            "Login"
          )}
        </Button>
      </div>
    </form>
  );
}
