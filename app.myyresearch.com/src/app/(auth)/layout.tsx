"use client";

import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import Image from "next/image";
import { useContext, useEffect, useState } from "react";
import { AuthContext } from "@/contexts/auth-contexts";
import { redirect } from "next/navigation";

export default function AuthLayout({
  children,
  ...props
}: {
  children: React.ReactNode;
}) {
  const auth = useContext(AuthContext);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (auth?.user) {
      redirect("/home");
    } else {
      setIsLoading(false);
    }
  }, [auth?.user]);

  if (isLoading) {
    return (
      <div className="flex min-h-svh items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col gap-6")} {...props}>
      <div className="flex min-h-svh flex-col items-center justify-center bg-muted p-6 md:p-10">
        <div className="w-full max-w-sm md:max-w-3xl">
          <Card className="overflow-hidden ">
            <CardContent className="grid p-0 md:grid-cols-2">
              {children}

              <div className="relative hidden h-full bg-muted md:block min-h-[500px]">
                <Image
                  src="/login.jpg"
                  alt="Image"
                  fill
                  className="object-cover dark:brightness-[0.2] dark:grayscale"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
