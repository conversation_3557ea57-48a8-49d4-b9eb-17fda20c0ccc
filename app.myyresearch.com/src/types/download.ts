import { DocumentStatus } from "./common";

export enum DownloadStatus {
  ACTIVE = "active",
  COMPLETED = "completed",
  EXPIRED = "expired",
}

export interface MinimalTemplateDownload {
  _id: string;
  user: string;
  templateId: string;
  templatePublicId: string;
  templateName: string;
  templateTitle: string;
  templateThumbnailUrl: string;
  downloadCount: number;
  lastAccessedAt: Date;
  createdAt: Date;
  updatedAt: Date;
  status?: DownloadStatus;
  documentStatus?: DocumentStatus;
}

export interface PaginatedTemplateDownloadsResponse {
  templateDownloads: MinimalTemplateDownload[];
  total: number;
  page: number;
  totalPages: number;
  hasMore: boolean;
}

export interface TemplateDownloadFilterParams {
  search?: string;
  page?: number;
  limit?: number;
  from?: string; // YYYY-MM-DD format
  to?: string; // YYYY-MM-DD format
  status?: DownloadStatus;
  documentStatus?: DocumentStatus;
}
