import { UploadedFile } from "./common";

export interface Course {
  id?: string;
  _id?: string;
  title: string;
  shortDescription: string;
  description?: string;
  thumbnail: UploadedFile;
  preview?: UploadedFile;
  whatYouWillLearn: string[];
  category: string;
  subcategory: string;
  isFree: boolean;
  price: number;
  discountPrice?: number;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy?: string;
  status: "draft" | "published";
  approvalStatus?: CourseApprovalStatus;
  approvalComment?: string;
  approvedBy?: string;
  approvalDate?: string;
  sections: CreateSectionDto[];
  creatorType?: CreatorType;
  instructorUsername?: string;
  enrollmentCount?: number;
}

export interface CreateCourseDto {
  title: string;
  shortDescription: string;
  description?: string;
  whatYouWillLearn: string[];
  thumbnail: UploadedFile;
  preview?: UploadedFile;
  category: string;
  subcategory: string;
  isFree: boolean;
  price: number;
  discountPrice?: number;
  status: "draft" | "published";
  sections: CreateSectionDto[];
}

export interface CreateSectionDto {
  _id?: string;
  title: string;
  description?: string;
  order: number;
  lessons: CreateLessonDto[];
}

export interface CreateLessonDto {
  _id?: string;
  title: string;
  isFree: boolean;
  description?: string;
  order: number;
  content: string;
  duration?: number;
  video?: UploadedFile;
  media?: UploadedFile;
  type: LessonType;
}

export enum LessonType {
  VIDEO = "video",
  TEXT = "text",
}

export enum CourseStatus {
  DRAFT = "draft",
  PUBLISHED = "published",
  PENDING = "pending",
}

export enum CreatorType {
  ADMIN = "admin",
  INSTRUCTOR = "instructor",
}

export enum CourseApprovalStatus {
  PENDING = "pending",
  APPROVED = "approved",
  REJECTED = "rejected",
}

export interface UploadFileDto {
  url: string;
  name: string;
  key: string;
  type: string;
  size: number;
}

export interface UpdateLessonDto extends CreateLessonDto {
  _id?: string;
}

export interface UpdateSectionDto extends Omit<CreateSectionDto, "lessons"> {
  _id?: string;
  lessons?: UpdateLessonDto[];
}

export interface UpdateCourseDto extends Omit<CreateCourseDto, "sections"> {
  sections?: UpdateSectionDto[];
}
