"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  createInstructorPaymentDetails,
  updateInstructorPaymentDetails,
  getInstructorPaymentDetails,
  getInstructorBalance,
  getInstructorPaymentHistory,
  createPaymentRequest,
  getInstructorPaymentRequests,
  getAdminInstructorBalances,
  getAdminInstructorBalanceById,
  createAdminPaymentRecord,
  getAdminPaymentHistory,
  getAdminPaymentRequests,
  updateAdminPaymentRequest,
  getAdminPaymentSummaryStats,
} from "@/lib/instructor-payments";
import {
  CreateInstructorPaymentDetailsRequest,
  UpdateInstructorPaymentDetailsRequest,
  CreatePaymentRequestRequest,
  CreatePaymentRecordRequest,
  UpdatePaymentRequestRequest,
  InstructorBalanceFilter,
} from "@/types/instructor-payments";

// Query keys for instructor payment operations
export const instructorPaymentKeys = {
  all: ["instructor-payments"] as const,
  lists: () => [...instructorPaymentKeys.all, "list"] as const,
  details: () => [...instructorPaymentKeys.all, "detail"] as const,
  paymentDetails: () =>
    [...instructorPaymentKeys.all, "payment-details"] as const,
  balance: () => [...instructorPaymentKeys.all, "balance"] as const,
  paymentHistory: () =>
    [...instructorPaymentKeys.all, "payment-history"] as const,
  paymentRequests: () =>
    [...instructorPaymentKeys.all, "payment-requests"] as const,
  admin: {
    all: ["instructor-payments", "admin"] as const,
    balances: (filter?: InstructorBalanceFilter) =>
      [...instructorPaymentKeys.admin.all, "balances", filter] as const,
    instructorBalance: (instructorId: string) =>
      [
        ...instructorPaymentKeys.admin.all,
        "instructor-balance",
        instructorId,
      ] as const,
    paymentHistory: () =>
      [...instructorPaymentKeys.admin.all, "payment-history"] as const,
    paymentRequests: () =>
      [...instructorPaymentKeys.admin.all, "payment-requests"] as const,
    stats: () => [...instructorPaymentKeys.admin.all, "stats"] as const,
  },
} as const;

/**
 * Hook to get instructor payment details
 */
export function useInstructorPaymentDetails() {
  return useQuery({
    queryKey: instructorPaymentKeys.paymentDetails(),
    queryFn: () => getInstructorPaymentDetails(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook to create instructor payment details
 */
export function useCreateInstructorPaymentDetails() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateInstructorPaymentDetailsRequest) =>
      createInstructorPaymentDetails(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: instructorPaymentKeys.paymentDetails(),
      });
    },
  });
}

/**
 * Hook to update instructor payment details
 */
export function useUpdateInstructorPaymentDetails() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateInstructorPaymentDetailsRequest) =>
      updateInstructorPaymentDetails(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: instructorPaymentKeys.paymentDetails(),
      });
    },
  });
}

/**
 * Hook to get instructor balance
 */
export function useInstructorBalance() {
  return useQuery({
    queryKey: instructorPaymentKeys.balance(),
    queryFn: () => getInstructorBalance(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook to get instructor payment history
 */
export function useInstructorPaymentHistory() {
  return useQuery({
    queryKey: instructorPaymentKeys.paymentHistory(),
    queryFn: () => getInstructorPaymentHistory(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook to get instructor payment requests
 */
export function useInstructorPaymentRequests() {
  return useQuery({
    queryKey: instructorPaymentKeys.paymentRequests(),
    queryFn: () => getInstructorPaymentRequests(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook to create a payment request
 */
export function useCreatePaymentRequest() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreatePaymentRequestRequest) =>
      createPaymentRequest(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: instructorPaymentKeys.paymentRequests(),
      });
      queryClient.invalidateQueries({
        queryKey: instructorPaymentKeys.balance(),
      });
    },
  });
}

/**
 * Hook to get all instructor balances (Admin only)
 */
export function useAdminInstructorBalances(filter?: InstructorBalanceFilter) {
  return useQuery({
    queryKey: instructorPaymentKeys.admin.balances(filter),
    queryFn: () => getAdminInstructorBalances(filter || {}),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    enabled: true, // Admin access is handled by the API
  });
}

/**
 * Hook to get a specific instructor balance by ID (Admin only)
 */
export function useAdminInstructorBalanceById(instructorId: string) {
  return useQuery({
    queryKey: instructorPaymentKeys.admin.instructorBalance(instructorId),
    queryFn: () => getAdminInstructorBalanceById(instructorId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    enabled: !!instructorId,
  });
}

/**
 * Hook to create admin payment record
 */
export function useCreateAdminPaymentRecord() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreatePaymentRecordRequest) =>
      createAdminPaymentRecord(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: instructorPaymentKeys.admin.balances(),
      });
      queryClient.invalidateQueries({
        queryKey: instructorPaymentKeys.admin.paymentHistory(),
      });
      queryClient.invalidateQueries({
        queryKey: instructorPaymentKeys.admin.stats(),
      });
    },
  });
}

/**
 * Hook to get admin payment history
 */
export function useAdminPaymentHistory() {
  return useQuery({
    queryKey: instructorPaymentKeys.admin.paymentHistory(),
    queryFn: () => getAdminPaymentHistory(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    enabled: true, // Admin access is handled by the API
  });
}

/**
 * Hook to get admin payment requests
 */
export function useAdminPaymentRequests() {
  return useQuery({
    queryKey: instructorPaymentKeys.admin.paymentRequests(),
    queryFn: () => getAdminPaymentRequests(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    enabled: true, // Admin access is handled by the API
  });
}

/**
 * Hook to update admin payment request
 */
export function useUpdateAdminPaymentRequest() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      requestId,
      data,
    }: {
      requestId: string;
      data: UpdatePaymentRequestRequest;
    }) => updateAdminPaymentRequest(requestId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: instructorPaymentKeys.admin.paymentRequests(),
      });
    },
  });
}

/**
 * Hook to get admin payment summary stats
 */
export function useAdminPaymentSummaryStats() {
  return useQuery({
    queryKey: instructorPaymentKeys.admin.stats(),
    queryFn: () => getAdminPaymentSummaryStats(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    enabled: true, // Admin access is handled by the API
  });
}
