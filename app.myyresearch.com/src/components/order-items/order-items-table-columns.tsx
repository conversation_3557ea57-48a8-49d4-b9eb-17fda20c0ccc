"use client";

import * as React from "react";
import { type ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { formatDate } from "@/lib/data-table/utils";
import { type DataTableRowAction } from "@/types";
import { Ellipsis, ExternalLink, Eye } from "lucide-react";
import { OrderItemDetails, OrderItemType, CreatorType } from "@/types/orders";
import Image from "next/image";

interface GetColumnsProps {
  setRowAction: React.Dispatch<
    React.SetStateAction<DataTableRowAction<OrderItemDetails> | null>
  >;
}

export function getColumns({
  setRowAction,
}: GetColumnsProps): ColumnDef<OrderItemDetails>[] {
  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          className="translate-y-0.5"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="translate-y-0.5"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "thumbnail",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Thumbnail" />
      ),
      cell: ({ row }) => {
        const thumbnail = row.getValue("thumbnail") as OrderItemDetails["thumbnail"];
        return (
          <div className="flex items-center justify-center w-12 h-12">
            {thumbnail?.url ? (
              <Image
                src={thumbnail.url}
                alt="Thumbnail"
                width={48}
                height={48}
                className="rounded-md object-cover"
              />
            ) : (
              <div className="w-12 h-12 bg-muted rounded-md flex items-center justify-center">
                <span className="text-xs text-muted-foreground">No Image</span>
              </div>
            )}
          </div>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: "itemTitle",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Title" />
      ),
      cell: ({ row }) => {
        const title = row.getValue("itemTitle") as string;
        const publicId = row.original.publicId;
        const itemType = row.original.itemType;
        
        return (
          <div className="flex flex-col space-y-1">
            <span className="font-medium line-clamp-2">{title}</span>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="text-xs">
                {publicId}
              </Badge>
              <Badge 
                variant={itemType === OrderItemType.COURSE ? "default" : "secondary"}
                className="text-xs"
              >
                {itemType === OrderItemType.COURSE ? "Course" : "Template"}
              </Badge>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "category",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Category" />
      ),
      cell: ({ row }) => {
        const category = row.getValue("category") as string;
        const subCategory = row.original.subCategory;
        
        return (
          <div className="flex flex-col space-y-1">
            <span className="font-medium">{category}</span>
            <span className="text-sm text-muted-foreground">{subCategory}</span>
          </div>
        );
      },
    },
    {
      accessorKey: "creatorType",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Creator" />
      ),
      cell: ({ row }) => {
        const creatorType = row.getValue("creatorType") as CreatorType;
        
        return (
          <Badge 
            variant={creatorType === CreatorType.MYRESEARCH ? "default" : "outline"}
            className="text-xs"
          >
            {creatorType === CreatorType.MYRESEARCH ? "MyResearch" : "Instructor"}
          </Badge>
        );
      },
    },
    {
      accessorKey: "totalPrice",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Price" />
      ),
      cell: ({ row }) => {
        const price = row.getValue("totalPrice") as number;
        const quantity = row.original.quantity;
        
        return (
          <div className="flex flex-col space-y-1">
            <span className="font-medium">${price.toFixed(2)}</span>
            <span className="text-sm text-muted-foreground">Qty: {quantity}</span>
          </div>
        );
      },
    },
    {
      accessorKey: "orderDate",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Order Date" />
      ),
      cell: ({ row }) => {
        const date = row.getValue("orderDate") as Date;
        return (
          <div className="flex flex-col space-y-1">
            <span>{formatDate(date)}</span>
            <span className="text-sm text-muted-foreground">
              Order: {row.original.orderId}
            </span>
          </div>
        );
      },
    },
    {
      id: "actions",
      cell: function Cell({ row }) {
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                aria-label="Open menu"
                variant="ghost"
                className="flex size-8 p-0 data-[state=open]:bg-muted"
              >
                <Ellipsis className="size-4" aria-hidden="true" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-40">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onSelect={() => setRowAction({ row, type: "view" })}
              >
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem
                onSelect={() => {
                  // Navigate to item details page
                  const itemType = row.original.itemType;
                  const publicId = row.original.publicId;
                  const url = itemType === OrderItemType.COURSE 
                    ? `/courses/${publicId}` 
                    : `/templates/${publicId}`;
                  window.open(url, '_blank');
                }}
              >
                <ExternalLink className="mr-2 h-4 w-4" />
                View Item
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];
}
