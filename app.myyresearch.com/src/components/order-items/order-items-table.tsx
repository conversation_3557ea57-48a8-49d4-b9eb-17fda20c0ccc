"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import {
  type DataTableFilterField,
  type DataTableAdvancedFilterField,
} from "@/types";
import { type DataTableRowAction } from "@/types";

import {
  OrderItemType,
  CreatorType,
  type OrderItemDetails,
} from "@/types/orders";
import { toSentenceCase } from "@/lib/data-table/utils";
import { useDataTable } from "@/hooks/data-table/use-data-table";
import { DataTable } from "@/components/data-table/data-table";
import { DataTableAdvancedToolbar } from "@/components/data-table/data-table-advanced-toolbar";
import { DataTableToolbar } from "@/components/data-table/data-table-toolbar";

import { getColumns } from "./order-items-table-columns";
import { OrderItemsTableToolbarActions } from "./order-items-table-toolbar-actions";

interface OrderItemsTableProps {
  data: OrderItemDetails[];
  pageCount: number;
  itemType?: OrderItemType;
  title?: string;
}

export function OrderItemsTable({
  data,
  pageCount,
  itemType,
  title = "Order Items",
}: OrderItemsTableProps) {
  const router = useRouter();

  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<OrderItemDetails> | null>(null);

  const columns = React.useMemo(
    () => getColumns({ setRowAction }),
    [setRowAction]
  );

  const filterFields: DataTableFilterField<OrderItemDetails>[] = [
    {
      id: "itemTitle",
      label: "Title",
      placeholder: "Filter by title...",
    },
    {
      id: "category",
      label: "Category",
      placeholder: "Filter by category...",
    },
    {
      id: "creatorType",
      label: "Creator Type",
      options: [
        { label: "MyResearch", value: CreatorType.MYRESEARCH },
        { label: "Instructor", value: CreatorType.INSTRUCTOR },
      ],
    },
  ];

  // Add item type filter only if not filtering by specific type
  if (!itemType) {
    filterFields.push({
      id: "itemType",
      label: "Type",
      options: [
        { label: "Course", value: OrderItemType.COURSE },
        { label: "Template", value: OrderItemType.TEMPLATE },
      ],
    });
  }

  const advancedFilterFields: DataTableAdvancedFilterField<OrderItemDetails>[] =
    [
      {
        id: "itemTitle",
        label: "Title",
        type: "text",
      },
      {
        id: "category",
        label: "Category",
        type: "text",
      },
      {
        id: "subCategory",
        label: "Subcategory",
        type: "text",
      },
      {
        id: "creatorType",
        label: "Creator Type",
        type: "multi-select",
        options: [
          { label: "MyResearch", value: CreatorType.MYRESEARCH },
          { label: "Instructor", value: CreatorType.INSTRUCTOR },
        ],
      },
      {
        id: "orderDate",
        label: "Order Date",
        type: "date",
      },
      {
        id: "totalPrice",
        label: "Price Range",
        type: "number",
      },
    ];

  // Add item type filter for advanced filters if not filtering by specific type
  if (!itemType) {
    advancedFilterFields.push({
      id: "itemType",
      label: "Item Type",
      type: "multi-select",
      options: [
        { label: "Course", value: OrderItemType.COURSE },
        { label: "Template", value: OrderItemType.TEMPLATE },
      ],
    });
  }

  const { table } = useDataTable({
    data,
    columns,
    pageCount,
    filterFields,
    enableAdvancedFilter: true,
    initialState: {
      sorting: [{ id: "orderDate", desc: true }],
      columnPinning: { right: ["actions"] },
    },
    getRowId: (originalRow) => originalRow._id,
  });

  return (
    <div className="space-y-4">
      <DataTableAdvancedToolbar
        table={table}
        filterFields={advancedFilterFields}
      >
        <OrderItemsTableToolbarActions table={table} />
      </DataTableAdvancedToolbar>
      <DataTable table={table} />
    </div>
  );
}
