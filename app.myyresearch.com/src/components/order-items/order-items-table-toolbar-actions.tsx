"use client";

import { type Table } from "@tanstack/react-table";
import { Download, RefreshCw } from "lucide-react";

import { type OrderItemDetails } from "@/types/orders";
import { exportTableToCSV } from "@/lib/data-table/export";
import { Button } from "@/components/ui/button";

interface OrderItemsTableToolbarActionsProps {
  table: Table<OrderItemDetails>;
  onRefresh?: () => void;
}

export function OrderItemsTableToolbarActions({
  table,
  onRefresh,
}: OrderItemsTableToolbarActionsProps) {
  return (
    <div className="flex items-center gap-2">
      {onRefresh && (
        <Button
          variant="outline"
          size="sm"
          onClick={onRefresh}
          className="ml-auto h-8 lg:flex"
        >
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh
        </Button>
      )}
      <Button
        variant="outline"
        size="sm"
        onClick={() =>
          exportTableToCSV(table, {
            filename: "order-items",
            excludeColumns: ["select", "actions"],
          })
        }
        className="ml-auto h-8 lg:flex"
      >
        <Download className="mr-2 h-4 w-4" />
        Export
      </Button>
    </div>
  );
}
