"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";

import { ApiStatus } from "@/types/common";
import {
  CreditCard,
  Building,
  Zap,
  AlertCircle,
  CheckCircle,
} from "lucide-react";
import {
  useCreateInstructorPaymentDetails,
  useInstructorPaymentDetails,
  useUpdateInstructorPaymentDetails,
} from "@/hooks/useInstructorPayments";
import { PaymentMethod } from "@/types/instructor-payments";

const paymentDetailsSchema = z
  .object({
    preferredPaymentMethod: z.nativeEnum(PaymentMethod),
    paypalEmail: z
      .string()
      .email("Invalid email address")
      .optional()
      .or(z.literal("")),
    bankAccountNumber: z.string().optional(),
    bankRoutingNumber: z.string().optional(),
    bankName: z.string().optional(),
    accountHolderName: z.string().optional(),
    swiftCode: z.string().optional(),
    stripeAccountId: z.string().optional(),
  })
  .refine(
    (data) => {
      if (data.preferredPaymentMethod === PaymentMethod.PAYPAL) {
        return data.paypalEmail && data.paypalEmail.length > 0;
      }
      if (data.preferredPaymentMethod === PaymentMethod.BANK_TRANSFER) {
        return (
          data.bankAccountNumber && data.bankName && data.accountHolderName
        );
      }
      if (data.preferredPaymentMethod === PaymentMethod.STRIPE) {
        return data.stripeAccountId && data.stripeAccountId.length > 0;
      }
      return true;
    },
    {
      message:
        "Please fill in all required fields for the selected payment method",
      path: ["preferredPaymentMethod"],
    }
  );

type PaymentDetailsFormData = z.infer<typeof paymentDetailsSchema>;

export function InstructorPaymentDetailsForm() {
  const {
    data: paymentDetails,
    isLoading,
    error,
  } = useInstructorPaymentDetails();
  const createPaymentDetails = useCreateInstructorPaymentDetails();
  const updatePaymentDetails = useUpdateInstructorPaymentDetails();

  const form = useForm<PaymentDetailsFormData>({
    resolver: zodResolver(paymentDetailsSchema),
    defaultValues: {
      preferredPaymentMethod: PaymentMethod.PAYPAL,
      paypalEmail: "",
      bankAccountNumber: "",
      bankRoutingNumber: "",
      bankName: "",
      accountHolderName: "",
      swiftCode: "",
      stripeAccountId: "",
    },
  });

  const watchedPaymentMethod = form.watch("preferredPaymentMethod");

  // Update form when payment details are loaded
  React.useEffect(() => {
    if (paymentDetails?.data) {
      const details = paymentDetails.data;
      form.reset({
        preferredPaymentMethod: details.preferredPaymentMethod,
        paypalEmail: details.paypalEmail || "",
        bankAccountNumber: details.bankAccountNumber || "",
        bankRoutingNumber: details.bankRoutingNumber || "",
        bankName: details.bankName || "",
        accountHolderName: details.accountHolderName || "",
        swiftCode: details.swiftCode || "",
        stripeAccountId: details.stripeAccountId || "",
      });
    }
  }, [paymentDetails]);

  const onSubmit = async (data: PaymentDetailsFormData) => {
    try {
      const isUpdate = paymentDetails?.data;
      const mutation = isUpdate ? updatePaymentDetails : createPaymentDetails;

      const response = await mutation.mutateAsync(data);

      if (response.status === ApiStatus.SUCCESS) {
        toast.success(
          isUpdate
            ? "Payment details updated successfully"
            : "Payment details created successfully"
        );
      } else {
        toast.error(response.message || "Failed to save payment details");
      }
    } catch (error) {
      toast.error("An error occurred while saving payment details");
      console.error("Payment details error:", error);
    }
  };

  const getPaymentMethodIcon = (method: PaymentMethod) => {
    switch (method) {
      case PaymentMethod.PAYPAL:
        return <CreditCard className="h-4 w-4" />;
      case PaymentMethod.BANK_TRANSFER:
        return <Building className="h-4 w-4" />;
      case PaymentMethod.STRIPE:
        return <Zap className="h-4 w-4" />;
      default:
        return <CreditCard className="h-4 w-4" />;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-48 w-full" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center gap-2 p-4 border border-red-200 rounded-lg bg-red-50">
        <AlertCircle className="h-5 w-5 text-red-600" />
        <span className="text-red-700">Failed to load payment details</span>
      </div>
    );
  }

  const hasExistingDetails = paymentDetails?.data;

  return (
    <div className="space-y-6">
      {hasExistingDetails && (
        <div className="flex items-center gap-2 p-4 border border-green-200 rounded-lg bg-green-50">
          <CheckCircle className="h-5 w-5 text-green-600" />
          <span className="text-green-700">Payment details configured</span>
          <Badge variant="outline" className="ml-auto">
            {hasExistingDetails.preferredPaymentMethod
              .replace("_", " ")
              .toUpperCase()}
          </Badge>
        </div>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="preferredPaymentMethod"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Preferred Payment Method</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select payment method" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value={PaymentMethod.PAYPAL}>
                      <div className="flex items-center gap-2">
                        <CreditCard className="h-4 w-4" />
                        PayPal
                      </div>
                    </SelectItem>
                    <SelectItem value={PaymentMethod.BANK_TRANSFER}>
                      <div className="flex items-center gap-2">
                        <Building className="h-4 w-4" />
                        Bank Transfer
                      </div>
                    </SelectItem>
                    <SelectItem value={PaymentMethod.STRIPE}>
                      <div className="flex items-center gap-2">
                        <Zap className="h-4 w-4" />
                        Stripe
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <Separator />

          {/* PayPal Fields */}
          {watchedPaymentMethod === PaymentMethod.PAYPAL && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  PayPal Information
                </CardTitle>
                <CardDescription>
                  Enter your PayPal email address to receive payments
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FormField
                  control={form.control}
                  name="paypalEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>PayPal Email Address *</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        This email must be associated with your PayPal account
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          )}

          {/* Bank Transfer Fields */}
          {watchedPaymentMethod === PaymentMethod.BANK_TRANSFER && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  Bank Transfer Information
                </CardTitle>
                <CardDescription>
                  Enter your bank account details for direct transfers
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="accountHolderName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Account Holder Name *</FormLabel>
                        <FormControl>
                          <Input placeholder="John Doe" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="bankName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Bank Name *</FormLabel>
                        <FormControl>
                          <Input placeholder="Chase Bank" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="bankAccountNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Account Number *</FormLabel>
                        <FormControl>
                          <Input placeholder="**********" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="bankRoutingNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Routing Number</FormLabel>
                        <FormControl>
                          <Input placeholder="*********" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <FormField
                  control={form.control}
                  name="swiftCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        SWIFT Code (for international transfers)
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="CHASUS33" {...field} />
                      </FormControl>
                      <FormDescription>
                        Required for international wire transfers
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          )}

          {/* Stripe Fields */}
          {watchedPaymentMethod === PaymentMethod.STRIPE && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  Stripe Information
                </CardTitle>
                <CardDescription>
                  Connect your Stripe account for fast payments
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FormField
                  control={form.control}
                  name="stripeAccountId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Stripe Account ID *</FormLabel>
                      <FormControl>
                        <Input placeholder="acct_**********" {...field} />
                      </FormControl>
                      <FormDescription>
                        Your Stripe Connect account ID
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          )}

          <div className="flex justify-end">
            <Button
              type="submit"
              disabled={
                createPaymentDetails.isPending || updatePaymentDetails.isPending
              }
            >
              {createPaymentDetails.isPending || updatePaymentDetails.isPending
                ? "Saving..."
                : hasExistingDetails
                  ? "Update Payment Details"
                  : "Save Payment Details"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
