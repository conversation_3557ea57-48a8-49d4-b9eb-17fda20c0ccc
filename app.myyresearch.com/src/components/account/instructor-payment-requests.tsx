"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  useInstructorPaymentRequests,
  useCreatePaymentRequest,
  useInstructorBalance,
} from "@/hooks/useInstructorPayments";
import { formatCurrency, formatDate } from "@/lib/utils";
import { ApiStatus } from "@/types/common";
import {
  FileText,
  AlertCircle,
  Plus,
  Clock,
  CheckCircle,
  XCircle,
  DollarSign,
} from "lucide-react";

const paymentRequestSchema = z.object({
  requestedAmount: z.number().min(1, "Amount must be greater than 0"),
  currency: z.string().default("USD"),
  requestNotes: z.string().optional(),
});

type PaymentRequestFormData = z.infer<typeof paymentRequestSchema>;

export function InstructorPaymentRequests() {
  const {
    data: paymentRequests,
    isLoading,
    error,
  } = useInstructorPaymentRequests();
  const { data: balanceData } = useInstructorBalance();
  const createPaymentRequest = useCreatePaymentRequest();
  const [isDialogOpen, setIsDialogOpen] = React.useState(false);

  const currentBalance = balanceData?.data?.currentBalance || 0;

  const form = useForm<PaymentRequestFormData>({
    resolver: zodResolver(paymentRequestSchema),
    defaultValues: {
      requestedAmount: 0,
      currency: "USD",
      requestNotes: "",
    },
  });

  // Update form values when balance data changes
  React.useEffect(() => {
    if (currentBalance > 0) {
      form.setValue("requestedAmount", currentBalance);
    }
  }, [currentBalance, form]);

  const onSubmit = async (data: PaymentRequestFormData) => {
    try {
      const response = await createPaymentRequest.mutateAsync(data);

      if (response.status === ApiStatus.SUCCESS) {
        toast.success("Payment request created successfully");
        setIsDialogOpen(false);
        form.reset();
      } else {
        toast.error(response.message || "Failed to create payment request");
      }
    } catch (error) {
      toast.error("An error occurred while creating payment request");
      console.error("Payment request error:", error);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case "pending":
        return (
          <Badge
            variant="outline"
            className="text-yellow-600 border-yellow-600"
          >
            Pending
          </Badge>
        );
      case "approved":
        return (
          <Badge variant="outline" className="text-blue-600 border-blue-600">
            Approved
          </Badge>
        );
      case "rejected":
        return (
          <Badge variant="outline" className="text-red-600 border-red-600">
            Rejected
          </Badge>
        );
      case "paid":
        return (
          <Badge variant="outline" className="text-green-600 border-green-600">
            Paid
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case "approved":
        return <CheckCircle className="h-4 w-4 text-blue-600" />;
      case "rejected":
        return <XCircle className="h-4 w-4 text-red-600" />;
      case "paid":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center gap-2 p-6">
          <AlertCircle className="h-5 w-5 text-red-600" />
          <span className="text-red-700">Failed to load payment requests</span>
        </CardContent>
      </Card>
    );
  }

  const requests = paymentRequests?.data || [];

  return (
    <div className="space-y-6">
      {/* Current Balance and Request Button */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Available Balance
            </div>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button
                  disabled={currentBalance <= 0}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Request Payment
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Request Payment</DialogTitle>
                  <DialogDescription>
                    Request a payment for your available balance. Maximum
                    amount: {formatCurrency(currentBalance)}
                  </DialogDescription>
                </DialogHeader>
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="space-y-4"
                  >
                    <FormField
                      control={form.control}
                      name="requestedAmount"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Requested Amount</FormLabel>
                          <FormControl>
                            <Input
                              type="text"
                              value={formatCurrency(currentBalance)}
                              disabled
                              className="bg-gray-50"
                            />
                          </FormControl>
                          <FormDescription>
                            Full available balance will be requested
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="requestNotes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Notes (Optional)</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Add any notes for this payment request..."
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <div className="flex justify-end gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setIsDialogOpen(false)}
                      >
                        Cancel
                      </Button>
                      <Button
                        type="submit"
                        disabled={createPaymentRequest.isPending}
                      >
                        {createPaymentRequest.isPending
                          ? "Creating..."
                          : "Create Request"}
                      </Button>
                    </div>
                  </form>
                </Form>
              </DialogContent>
            </Dialog>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold text-green-600">
            {formatCurrency(currentBalance)}
          </div>
          <p className="text-gray-600 mt-1">
            {currentBalance > 0
              ? "You can request a payment for this amount"
              : "No balance available for payment request"}
          </p>
        </CardContent>
      </Card>

      {/* Payment Requests Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Payment Requests ({requests.length})
          </CardTitle>
          <CardDescription>
            Track the status of your payment requests
          </CardDescription>
        </CardHeader>
        <CardContent>
          {requests.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No Payment Requests
              </h3>
              <p className="text-gray-500 mb-4">
                You haven't made any payment requests yet
              </p>
              {currentBalance > 0 && (
                <Button onClick={() => setIsDialogOpen(true)}>
                  Create Your First Request
                </Button>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date Requested</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Notes</TableHead>
                  <TableHead>Admin Notes</TableHead>
                  <TableHead>Last Updated</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {requests.map((request) => (
                  <TableRow key={request._id}>
                    <TableCell>
                      <div className="font-medium">
                        {formatDate(request.createdAt, {
                          month: "short",
                          day: "numeric",
                          year: "numeric",
                        })}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-mono font-medium">
                        {formatCurrency(
                          request.requestedAmount,
                          request.currency
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(request.status)}
                        {getStatusBadge(request.status)}
                      </div>
                    </TableCell>
                    <TableCell>
                      {request.requestNotes ? (
                        <div className="text-sm text-gray-600 max-w-xs truncate">
                          {request.requestNotes}
                        </div>
                      ) : (
                        <span className="text-gray-400">—</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {request.adminNotes ? (
                        <div className="text-sm text-gray-600 max-w-xs truncate">
                          {request.adminNotes}
                        </div>
                      ) : (
                        <span className="text-gray-400">—</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="text-sm text-gray-500">
                        {formatDate(request.updatedAt, {
                          month: "short",
                          day: "numeric",
                          hour: "2-digit",
                          minute: "2-digit",
                        })}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
