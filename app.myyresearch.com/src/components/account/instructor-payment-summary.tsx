"use client";

import * as React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Separator } from "@/components/ui/separator";
import { useInstructorBalance } from "@/hooks/useInstructorPayments";
import { formatCurrency, formatDate } from "@/lib/utils";
import {
  DollarSign,
  TrendingUp,
  Clock,
  Package,
  AlertCircle,
  Calendar,
  CreditCard,
} from "lucide-react";

export function InstructorPaymentSummary() {
  const { data: balanceData, isLoading, error } = useInstructorBalance();

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Skeleton key={i} className="h-32" />
          ))}
        </div>
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center gap-2 p-6">
          <AlertCircle className="h-5 w-5 text-red-600" />
          <span className="text-red-700">Failed to load payment summary</span>
        </CardContent>
      </Card>
    );
  }

  if (!balanceData?.data) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <div className="text-gray-500">No payment data available</div>
        </CardContent>
      </Card>
    );
  }

  const balance = balanceData.data;

  const summaryCards = [
    {
      title: "Current Balance",
      value: formatCurrency(balance.currentBalance),
      description: "Available for withdrawal",
      icon: DollarSign,
      color: balance.currentBalance > 0 ? "text-green-600" : "text-gray-600",
      bgColor: balance.currentBalance > 0 ? "bg-green-50" : "bg-gray-50",
    },
    {
      title: "Total Earnings",
      value: formatCurrency(balance.totalEarnings),
      description: "All-time earnings",
      icon: TrendingUp,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      title: "Total Paid",
      value: formatCurrency(balance.totalPaid),
      description: "Amount received",
      icon: CreditCard,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
    },
    {
      title: "Unpaid Items",
      value: balance.unpaidOrderItems.length.toString(),
      description: "Items pending payment",
      icon: Package,
      color: "text-orange-600",
      bgColor: "bg-orange-50",
    },
  ];

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {summaryCards.map((card, index) => {
          const Icon = card.icon;
          return (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {card.title}
                </CardTitle>
                <div className={`p-2 rounded-lg ${card.bgColor}`}>
                  <Icon className={`h-4 w-4 ${card.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{card.value}</div>
                <p className="text-xs text-muted-foreground">
                  {card.description}
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Unpaid Order Items */}
      {balance.unpaidOrderItems.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Unpaid Order Items ({balance.unpaidOrderItems.length})
            </CardTitle>
            <CardDescription>
              Items that haven't been paid out yet
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {balance.unpaidOrderItems.map((item) => (
                <div
                  key={item._id}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        {item.itemType}
                      </Badge>
                      <span className="font-medium">{item.itemTitle}</span>
                    </div>
                    <div className="text-sm text-muted-foreground mt-1">
                      Order Date: {formatDate(item.orderDate)} • ID:{" "}
                      {item.publicId}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-mono text-sm">
                      {formatCurrency(item.totalPrice)}
                    </div>
                    <div className="font-mono text-xs text-green-600">
                      Your Earning: {formatCurrency(item.instructorEarning)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Payment History */}
      {balance.paymentHistory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Recent Payment History
            </CardTitle>
            <CardDescription>Your recent payment transactions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {balance.paymentHistory.map((payment) => (
                <div
                  key={payment._id}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      <span className="font-medium">
                        {formatDate(payment.paymentDate)}
                      </span>
                      <Badge variant="outline" className="text-xs">
                        {payment.paymentMethod}
                      </Badge>
                    </div>
                    {payment.notes && (
                      <div className="text-sm text-muted-foreground mt-1">
                        {payment.notes}
                      </div>
                    )}
                    <div className="text-xs text-muted-foreground mt-1">
                      {payment.orderItemsCount} items
                      {payment.transactionId &&
                        ` • TXN: ${payment.transactionId}`}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-mono font-medium text-green-600">
                      {formatCurrency(payment.amount)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* No Payment History */}
      {balance.paymentHistory.length === 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Payment History
            </CardTitle>
            <CardDescription>Your payment transaction history</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8 text-gray-500">
              <Clock className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No payment history yet</p>
              <p className="text-sm">
                Payments will appear here once processed
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      {balance.currentBalance > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Request Payment</CardTitle>
            <CardDescription>
              You have {formatCurrency(balance.currentBalance)} available for
              withdrawal
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">
                  Request a payment for your current balance. Payments are
                  typically processed within 3-5 business days.
                </p>
              </div>
              <Button>Request Payment</Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* No Balance */}
      {balance.currentBalance === 0 &&
        balance.unpaidOrderItems.length === 0 && (
          <Card>
            <CardContent className="text-center py-8">
              <DollarSign className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p className="text-gray-500">No pending payments</p>
              <p className="text-sm text-gray-400">
                Your earnings will appear here when you make sales
              </p>
            </CardContent>
          </Card>
        )}
    </div>
  );
}
