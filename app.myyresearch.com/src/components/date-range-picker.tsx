"use client";

import * as React from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { addDays, format } from "date-fns";
import type { DateRange } from "react-day-picker";

import { type ButtonProps } from "@/components/ui/button";
import { PopoverContent } from "@/components/ui/popover";
import { CalendarDatePicker } from "@/components/ui/calendar-date-picker";

interface DateRangePickerProps
  extends React.ComponentPropsWithoutRef<typeof PopoverContent> {
  /**
   * The selected date range.
   * @default undefined
   * @type DateRange
   * @example { from: new Date(), to: new Date() }
   */
  dateRange?: DateRange;

  /**
   * The number of days to display in the date range picker.
   * @default undefined
   * @type number
   * @example 7
   */
  dayCount?: number;

  /**
   * The placeholder text of the calendar trigger button.
   * @default "Pick a date"
   * @type string | undefined
   */
  placeholder?: string;

  /**
   * The variant of the calendar trigger button.
   * @default "outline"
   * @type "default" | "outline" | "secondary" | "ghost"
   */
  triggerVariant?: Exclude<ButtonProps["variant"], "destructive" | "link">;

  /**
   * The size of the calendar trigger button.
   * @default "default"
   * @type "default" | "sm" | "lg"
   */
  triggerSize?: Exclude<ButtonProps["size"], "icon">;

  /**
   * The class name of the calendar trigger button.
   * @default undefined
   * @type string
   */
  triggerClassName?: string;
}

export function DateRangePicker({ dateRange, dayCount }: DateRangePickerProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [date, setDate] = React.useState<DateRange | undefined>(() => {
    const fromParam = searchParams.get("from");
    const toParam = searchParams.get("to");

    let fromDay: Date | undefined;
    let toDay: Date | undefined;

    if (dateRange) {
      fromDay = dateRange.from;
      toDay = dateRange.to;
    } else if (dayCount) {
      toDay = new Date();
      fromDay = addDays(toDay, -dayCount);
    }

    return {
      from: fromParam ? new Date(fromParam) : fromDay,
      to: toParam ? new Date(toParam) : toDay,
    };
  });

  // Update query string
  React.useEffect(() => {
    const newSearchParams = new URLSearchParams(searchParams);
    if (date?.from) {
      newSearchParams.set("from", format(date.from, "yyyy-MM-dd"));
    } else {
      newSearchParams.delete("from");
    }

    if (date?.to) {
      newSearchParams.set("to", format(date.to, "yyyy-MM-dd"));
    } else {
      newSearchParams.delete("to");
    }

    router.replace(`${pathname}?${newSearchParams.toString()}`, {
      scroll: false,
    });
  }, [date?.from, date?.to, pathname, router, searchParams]);

  const handleDateSelect = (selectedDate: DateRange) => {
    setDate(selectedDate);
  };

  return (
    <div className="grid gap-2">
      <div className="flex items-right justify-end gap-2">
        <CalendarDatePicker
          date={date ?? { from: new Date(), to: new Date() }}
          onDateSelect={handleDateSelect}
          numberOfMonths={2}
          variant="outline"
        />
      </div>
    </div>
  );
}
