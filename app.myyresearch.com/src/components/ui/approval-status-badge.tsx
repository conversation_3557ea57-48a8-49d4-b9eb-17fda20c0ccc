import { Badge } from "@/components/ui/badge";
import { CheckCircle2, Clock, XCircle } from "lucide-react";
import { CourseApprovalStatus } from "@/types/course";
import { TemplateApprovalStatus } from "@/types/template";

type ApprovalStatus = CourseApprovalStatus | TemplateApprovalStatus;

interface ApprovalStatusBadgeProps {
  status: ApprovalStatus;
  className?: string;
}

export function ApprovalStatusBadge({ status, className }: ApprovalStatusBadgeProps) {
  const getStatusConfig = (status: ApprovalStatus) => {
    switch (status) {
      case CourseApprovalStatus.APPROVED:
      case TemplateApprovalStatus.APPROVED:
        return {
          variant: "default" as const,
          icon: CheckCircle2,
          label: "Approved",
          className: "bg-green-100 text-green-800 hover:bg-green-100",
        };
      case CourseApprovalStatus.PENDING:
      case TemplateApprovalStatus.PENDING:
        return {
          variant: "secondary" as const,
          icon: Clock,
          label: "Pending",
          className: "bg-yellow-100 text-yellow-800 hover:bg-yellow-100",
        };
      case CourseApprovalStatus.REJECTED:
      case TemplateApprovalStatus.REJECTED:
        return {
          variant: "destructive" as const,
          icon: XCircle,
          label: "Rejected",
          className: "bg-red-100 text-red-800 hover:bg-red-100",
        };
      default:
        return {
          variant: "outline" as const,
          icon: Clock,
          label: "Unknown",
          className: "",
        };
    }
  };

  const config = getStatusConfig(status);
  const Icon = config.icon;

  return (
    <Badge 
      variant={config.variant} 
      className={`gap-1 ${config.className} ${className || ""}`}
    >
      <Icon className="h-3 w-3" />
      {config.label}
    </Badge>
  );
}
