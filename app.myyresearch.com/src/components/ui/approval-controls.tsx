"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CheckCircle2, XCircle, Clock, MessageSquare } from "lucide-react";
import { CourseApprovalStatus } from "@/types/course";
import { TemplateApprovalStatus } from "@/types/template";
import { ApprovalStatusBadge } from "./approval-status-badge";

type ApprovalStatus = CourseApprovalStatus | TemplateApprovalStatus;

interface ApprovalControlsProps {
  currentStatus: ApprovalStatus;
  onApprove: (status: ApprovalStatus, comment?: string) => Promise<void>;
  isLoading?: boolean;
  className?: string;
}

export function ApprovalControls({
  currentStatus,
  onApprove,
  isLoading = false,
  className,
}: ApprovalControlsProps) {
  const [selectedStatus, setSelectedStatus] =
    useState<ApprovalStatus>(currentStatus);
  const [comment, setComment] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (selectedStatus === currentStatus && !comment.trim()) {
      return; // No changes to submit
    }

    setIsSubmitting(true);
    try {
      await onApprove(selectedStatus, comment.trim() || undefined);
      setComment(""); // Clear comment after successful submission
    } catch (error) {
      console.error("Failed to update approval status:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const hasChanges =
    selectedStatus !== currentStatus || comment.trim().length > 0;

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          Approval Status
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label>Current Status</Label>
          <ApprovalStatusBadge status={currentStatus} />
        </div>

        <div className="space-y-2">
          <Label htmlFor="status-select">Change Status</Label>
          <Select
            value={selectedStatus}
            onValueChange={(value) =>
              setSelectedStatus(value as ApprovalStatus)
            }
            disabled={isLoading || isSubmitting}
          >
            <SelectTrigger id="status-select">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="pending">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-yellow-600" />
                  Pending
                </div>
              </SelectItem>
              <SelectItem value="approved">
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                  Approved
                </div>
              </SelectItem>
              <SelectItem value="rejected">
                <div className="flex items-center gap-2">
                  <XCircle className="h-4 w-4 text-red-600" />
                  Rejected
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="comment">Comment (Optional)</Label>
          <Textarea
            id="comment"
            placeholder="Add a comment about this approval decision..."
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            disabled={isLoading || isSubmitting}
            rows={3}
          />
        </div>

        <Button
          onClick={handleSubmit}
          disabled={!hasChanges || isLoading || isSubmitting}
          className="w-full"
        >
          {isSubmitting ? "Updating..." : "Update Approval Status"}
        </Button>
      </CardContent>
    </Card>
  );
}
