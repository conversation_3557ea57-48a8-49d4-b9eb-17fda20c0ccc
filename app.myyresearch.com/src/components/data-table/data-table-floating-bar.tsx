"use client";

import * as React from "react";
import type { Table } from "@tanstack/react-table";
import { X } from "lucide-react";

import { Button } from "@/components/ui/button";

interface DataTableFloatingBarProps<TData> {
  table: Table<TData>;
  children?: React.ReactNode;
}

export function DataTableFloatingBar<TData>({
  table,
  children,
}: DataTableFloatingBarProps<TData>) {
  const selectedRowsCount = table.getFilteredSelectedRowModel().rows.length;

  if (selectedRowsCount === 0) {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-1/2 z-40 min-w-[300px] -translate-x-1/2 rounded-lg border bg-background p-4 shadow-lg">
      <div className="flex items-center justify-between gap-2">
        <div className="text-sm font-medium">
          {selectedRowsCount} row{selectedRowsCount > 1 ? "s" : ""} selected
        </div>
        <div className="flex items-center gap-2">
          {children}
          <Button
            aria-label="Clear selection"
            variant="ghost"
            size="sm"
            onClick={() => table.toggleAllRowsSelected(false)}
            className="h-8 px-2"
          >
            <X className="size-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
