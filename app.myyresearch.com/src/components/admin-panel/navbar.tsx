"use client";

import { UserNav } from "@/components/admin-panel/user-nav";
import { SheetMenu } from "@/components/admin-panel/sheet-menu";
import { ModeToggle } from "@/components/mode-toggle";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useSidebar } from "@/hooks/use-sidebar";
import { useStore } from "@/hooks/use-store";

interface NavbarProps {
  title: string;
}

export function Navbar({ title }: NavbarProps) {
  const sidebar = useStore(useSidebar, (x) => x);
  if (!sidebar) return null;
  const { settings, setSettings } = sidebar;

  return (
    <header className="sticky top-0 z-10 w-full bg-background/95 shadow backdrop-blur supports-[backdrop-filter]:bg-background/60 dark:shadow-secondary">
      <div className="mx-4 sm:mx-8 flex h-14 items-center">
        <div className="flex items-center space-x-4 lg:space-x-0">
          <SheetMenu />
          <h1 className="font-bold">{title}</h1>
        </div>
        <div className="flex flex-1 items-center justify-end space-x-4">
          <TooltipProvider>
            <div className="flex items-center gap-4">
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="navbar-hover-open"
                      onCheckedChange={(x) => setSettings({ isHoverOpen: x })}
                      checked={settings.isHoverOpen}
                    />
                    <Label htmlFor="navbar-hover-open">Hover</Label>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    When hovering on the sidebar in mini state, it will open
                  </p>
                </TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="navbar-disable-sidebar"
                      onCheckedChange={(x) => setSettings({ disabled: x })}
                      checked={settings.disabled}
                    />
                    <Label htmlFor="navbar-disable-sidebar">Hide</Label>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Hide sidebar</p>
                </TooltipContent>
              </Tooltip>
            </div>
          </TooltipProvider>
          <ModeToggle />
          <UserNav />
        </div>
      </div>
    </header>
  );
}
