import React from "react";
import { NodeViewWrapper, NodeViewProps } from "@tiptap/react";

export const YouTubeComponent: React.FC<NodeViewProps> = ({ node }) => {
  const { src, width, height } = node.attrs;

  return (
    <NodeViewWrapper className="youtube-embed">
      <iframe
        width={width}
        height={height}
        src={src}
        frameBorder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen
      />
    </NodeViewWrapper>
  );
};
