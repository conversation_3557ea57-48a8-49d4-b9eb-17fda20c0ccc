"use client";

import {
  Heading1,
  Heading2,
  Heading3,
  Heading4,
  Chev<PERSON>Down,
  Check,
} from "lucide-react";
import React from "react";

import { cn } from "@/lib/utils";
import { useToolbar } from "../providers/toolbar-provider";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

const headingOptions = [
  { level: 1, icon: Heading1, label: "Heading 1" },
  { level: 2, icon: Heading2, label: "Heading 2" },
  { level: 3, icon: Heading3, label: "Heading 3" },
  { level: 4, icon: Heading4, label: "Heading 4" },
];

export function HeadingToolbar() {
  const { editor } = useToolbar();

  const activeHeading = headingOptions.find((option) =>
    editor?.isActive("heading", { level: option.level })
  );

  const setHeading = (level: number) => {
    //@ts-ignore
    editor?.chain().focus().toggleHeading({ level }).run();
  };

  return (
    <DropdownMenu>
      <Tooltip>
        <TooltipTrigger asChild>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className={cn(
                "h-8 w-max px-3 font-normal",
                activeHeading && "bg-accent"
              )}
            >
              {activeHeading ? (
                <activeHeading.icon className="mr-2 h-4 w-4" />
              ) : (
                <Heading1 className="mr-2 h-4 w-4" />
              )}
              {activeHeading ? activeHeading.label : "Normal Text"}
              <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
        </TooltipTrigger>
        <TooltipContent>
          <span>Heading</span>
        </TooltipContent>
      </Tooltip>
      <DropdownMenuContent align="start" className="w-48">
        <DropdownMenuItem
          onSelect={() => editor?.chain().focus().setParagraph().run()}
        >
          <Heading1 className="mr-2 h-4 w-4" />
          Normal Text
          {!activeHeading && <Check className="ml-auto h-4 w-4" />}
        </DropdownMenuItem>
        {headingOptions.map((option) => (
          <DropdownMenuItem
            key={option.level}
            onSelect={() => setHeading(option.level)}
          >
            <option.icon className="mr-2 h-4 w-4" />
            {option.label}
            {activeHeading?.level === option.level && (
              <Check className="ml-auto h-4 w-4" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
