"use client";

import { Youtube } from "lucide-react";
import React, { useState } from "react";

import { cn } from "@/lib/utils";
import { useToolbar } from "../providers/toolbar-provider";
import { Button, type ButtonProps } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

const YouTubeToolbar = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, onClick, children, ...props }, ref) => {
    const { editor } = useToolbar();
    const [youtubeUrl, setYoutubeUrl] = useState("");
    const [width, setWidth] = useState(640);
    const [height, setHeight] = useState(480);

    const handleInsertYoutube = () => {
      if (youtubeUrl) {
        editor
          ?.chain()
          .focus()
          .setYoutubeVideo({
            src: youtubeUrl,
            width: Math.max(320, width),
            height: Math.max(180, height),
          })
          .run();
        setYoutubeUrl("");
      }
    };

    return (
      <Popover>
        <Tooltip>
          <TooltipTrigger asChild>
            <PopoverTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className={cn("h-8 w-8", className)}
                ref={ref}
                {...props}
              >
                {children || <Youtube className="h-4 w-4" />}
              </Button>
            </PopoverTrigger>
          </TooltipTrigger>
          <TooltipContent>
            <span>YouTube</span>
          </TooltipContent>
        </Tooltip>

        <PopoverContent className="w-80">
          <div className="grid gap-4">
            <div className="space-y-2">
              <h4 className="font-medium leading-none">Insert YouTube Video</h4>
              <p className="text-sm text-muted-foreground">
                Enter the YouTube video URL to embed.
              </p>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="youtube-url">YouTube URL</Label>
              <Input
                id="youtube-url"
                value={youtubeUrl}
                onChange={(e) => setYoutubeUrl(e.target.value)}
                placeholder="https://www.youtube.com/watch?v=..."
              />
            </div>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label htmlFor="width">Width</Label>
                <Input
                  id="width"
                  type="number"
                  min="320"
                  max="1024"
                  value={width}
                  onChange={(e) => setWidth(Number(e.target.value))}
                />
              </div>
              <div>
                <Label htmlFor="height">Height</Label>
                <Input
                  id="height"
                  type="number"
                  min="180"
                  max="720"
                  value={height}
                  onChange={(e) => setHeight(Number(e.target.value))}
                />
              </div>
            </div>
            <Button onClick={handleInsertYoutube}>Insert Video</Button>
          </div>
        </PopoverContent>
      </Popover>
    );
  }
);

YouTubeToolbar.displayName = "YouTubeToolbar";

export { YouTubeToolbar };
