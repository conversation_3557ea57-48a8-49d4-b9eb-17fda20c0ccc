import Link from "next/link";
import Image from "next/image";
import { ArrowLeft } from "lucide-react";

const StudioNavbar = (props: any) => {
  return (
    <div>
      <div className="p-5  text-gray-100 flex items-center justify-between">
        <Link href={"/"} className="flex items-center gap-3  duration-200">
          <ArrowLeft className="h-6 w-6 text-black" />
          <Image
            src="/images/logo/logo.png"
            alt="Logo"
            width={180}
            height={32}
          />
        </Link>
        {/* <Logo className="text-2xl hidden md:inline-flex" /> */}
      </div>
      {props.renderDefault(props)}
    </div>
  );
};

export default StudioNavbar;
