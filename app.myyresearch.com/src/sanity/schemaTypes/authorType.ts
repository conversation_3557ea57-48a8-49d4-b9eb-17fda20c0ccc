import { UserIcon } from "@sanity/icons";
import { defineArrayMember, defineField, defineType } from "sanity";

export const authorType = defineType({
  name: "author",
  title: "Author",
  type: "document",
  fields: [
    define<PERSON><PERSON>({
      name: "name",
      type: "string"
    }),
    define<PERSON><PERSON>({
      name: "slug",
      type: "slug",
      options: {
        source: "name"
      }
    }),
    defineField({
      name: "twitter",
      title: "Twitter Handle",
      type: "string",
      description: "Twitter username without the @",
      validation: (Rule) =>
        Rule.regex(/^[A-Za-z0-9_]{1,15}$/, {
          name: "twitter handle",
          invert: false
        })
    }),
    defineField({
      name: "image",
      type: "image",
      options: {
        hotspot: true
      }
    }),
    defineField({
      name: "bio",
      type: "array",
      of: [
        defineArrayMember({
          type: "block",
          styles: [{ title: "Normal", value: "normal" }],
          lists: []
        })
      ]
    })
  ],
  preview: {
    select: {
      title: "name",
      media: "image"
    }
  }
});
