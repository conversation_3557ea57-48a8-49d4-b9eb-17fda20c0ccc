import { DocumentTextIcon } from "@sanity/icons";
import { defineArrayMember, defineField, defineType } from "sanity";

export const postType = defineType({
  name: "post",
  title: "Post",
  type: "document",
  fields: [
    define<PERSON>ield({
      name: "title",
      type: "string"
    }),
    define<PERSON><PERSON>({
      name: "slug",
      type: "slug",
      options: {
        source: "title"
      }
    }),
    defineField({
      name: "description",
      title: "Description",
      type: "string"
    }),
    defineField({
      name: "author",
      type: "reference",
      to: { type: "author" }
    }),
    defineField({
      name: "mainImage",
      type: "image",
      options: {
        hotspot: true
      },
      fields: [
        {
          name: "alt",
          type: "string",
          title: "Alternative text"
        }
      ]
    }),
    defineField({
      name: "categories",
      type: "array",
      of: [defineArrayMember({ type: "reference", to: { type: "category" } })]
    }),
    define<PERSON>ield({
      name: "publishedAt",
      type: "datetime"
    }),
    define<PERSON>ield({
      name: "body",
      type: "blockContent"
    }),
    define<PERSON><PERSON>({
      name: "advertiestment",
      type: "reference",
      to: { type: "advertiestment" }
    })
  ],
  preview: {
    select: {
      title: "title",
      author: "author.name",
      media: "mainImage"
    },
    prepare(selection) {
      const { author } = selection;
      return { ...selection, subtitle: author && `by ${author}` };
    }
  }
});
