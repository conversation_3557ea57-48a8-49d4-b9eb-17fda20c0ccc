import { AddCircleIcon } from "@sanity/icons";
import { defineField, defineType } from "sanity";

export const advertiestmentType = defineType({
  name: "advertiestment",
  title: "Advertiestment",
  type: "document",

  fields: [
    defineField({
      name: "name",
      type: "string"
    }),
    defineField({
      name: "image",
      type: "image",
      options: {
        hotspot: true
      }
    }),
    defineField({
      name: "link",
      title: "Link",
      type: "string",
      description: "Link to the advertiestment",
      validation: (Rule) =>
        Rule.regex(
          /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/,
          {
            name: "link",
            invert: false
          }
        ).error("Please enter a valid URL")
    })
  ],
  preview: {
    select: {
      title: "name",
      media: "image"
    }
  }
});
