"use client";

/**
 * This configuration is used to for the Sanity Studio that’s mounted on the `/app/studio/[[...tool]]/page.tsx` route
 */

import { visionTool } from "@sanity/vision";
import { defineConfig } from "sanity";
import { structureTool } from "sanity/structure";
import { schema } from "@/sanity/schemaTypes";
import { structure } from "@/sanity/structure";
import StudioNavbar from "@/components/studio-navbar";
import { dataset, projectId } from "@/sanity/env";

export default defineConfig({
  basePath: "/studio",
  projectId,
  dataset,
  // Add and edit the content schema in the './sanity/schemaTypes' folder
  schema,
  plugins: [
    structureTool({ structure }),
    // // Vision is for querying with GROQ from inside the Studio
    // // https://www.sanity.io/docs/the-vision-plugin
    // visionTool({ defaultApiVersion: apiVersion })
  ],
  studio: {
    components: {
      navbar: StudioNavbar,
    },
  },
});

export enum SanityValidationTags {
  FAQ = "faq",
  GALLERY = "gallery",
  PRICING = "pricing",
  TEAM = "team",
  TESTIMONIALS = "testimonials",
  CONTACT = "contact",
  HERO = "hero",
  FEATURES = "features",
  CTA = "cta",
  FOOTER = "footer",
  NAVIGATION = "navigation",
  SOCIAL = "social",
  COPYRIGHT = "copyright",
  LOGO = "logo",
  ICON = "icon",
}
