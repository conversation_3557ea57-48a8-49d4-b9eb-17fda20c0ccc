# Admin Panel Integration for Purchased Items Management

## Overview

This implementation provides a comprehensive admin panel integration for managing purchased items (templates and courses) using the existing `purchased-items` API endpoints. The solution follows the established patterns in the codebase and provides separate views for downloads (templates) and enrollments (courses).

## Backend Integration

### API Endpoint
- **Endpoint**: `GET /api/v1/users/purchased-items/admin/all`
- **Access Control**: Admin-only with `@UseGuards(JwtAuthGuard, RolesGuard)` and `@Roles(UserRole.ADMIN)`
- **Filtering**: Uses `GetPurchasedItemsFilterDto` to filter by `itemType` (COURSE vs TEMPLATE)

### Response Structure
```typescript
interface GetPurchasedItemsResponse {
  items: PurchasedItem[];
  total: number;
  page: number;
  totalPages: number;
  success: boolean;
  message: string;
}
```

## Frontend Implementation

### File Structure
```
app.myyresearch.com/src/
├── types/purchased-items.ts                    # TypeScript interfaces
├── lib/purchased-items.ts                      # API integration functions
├── hooks/use-purchased-items.ts                # TanStack Query hooks
├── app/(admin)/downloads/
│   ├── page.tsx                               # Downloads page (templates)
│   ├── _components/purchased-templates-table.tsx
│   ├── _lib/queries.ts                        # Server-side queries
│   └── _lib/validations.ts                    # Validation schemas
└── app/(admin)/enrollments/
    ├── page.tsx                               # Enrollments page (courses)
    ├── _components/purchased-courses-table.tsx
    ├── _lib/queries.ts                        # Server-side queries
    └── _lib/validations.ts                     # Validation schemas
```

### Key Features

#### 1. Downloads Page (`/admin/downloads`)
- **Purpose**: Display purchased template items
- **Filtering**: Automatically filters for `itemType: TEMPLATE`
- **Data Display**: 
  - Template ID (publicId)
  - Title
  - Category
  - Price (sellingPrice)
  - Creator Type
  - Purchase Date
  - Order ID
- **Actions**: View Details, Download

#### 2. Enrollments Page (`/admin/enrollments`)
- **Purpose**: Display purchased course items
- **Filtering**: Automatically filters for `itemType: COURSE`
- **Data Display**:
  - Course ID (publicId)
  - Title
  - Category
  - Price (sellingPrice)
  - Creator Type
  - Enrollment Date (purchasedDate)
  - Order ID
- **Actions**: View Details, View Course

#### 3. Common Features
- **Search**: By title and public ID
- **Filtering**: By category and creator type
- **Pagination**: Server-side pagination with configurable page size
- **Sorting**: By purchase date (default), title, price, etc.
- **Loading States**: Skeleton components during data fetching
- **Error Handling**: Proper error boundaries and fallbacks
- **Responsive Design**: Mobile-friendly table layouts

### Technical Implementation

#### TypeScript Types
```typescript
export enum ItemType {
  COURSE = 'course',
  TEMPLATE = 'template',
}

export enum CreatorType {
  INSTRUCTOR = 'instructor',
  MYRESEARCH = 'myresearch',
}

export interface PurchasedItem {
  _id: string;
  purchasedBy?: string;
  itemType: ItemType;
  itemId: string;
  publicId: string;
  title: string;
  category: string;
  subCategory: string;
  creatorType: CreatorType;
  createdBy: string;
  sellingPrice: number;
  purchasedDate: Date;
  orderId: string;
  paymentId: string;
  // ... additional fields
}
```

#### API Integration
```typescript
// Get purchased templates (Admin only)
export async function getAdminPurchasedTemplates(
  params: Omit<PurchasedItemsFilterParams, 'itemType'>
): Promise<ApiResponse<GetPurchasedItemsResponse>>

// Get purchased courses (Admin only)
export async function getAdminPurchasedCourses(
  params: Omit<PurchasedItemsFilterParams, 'itemType'>
): Promise<ApiResponse<GetPurchasedItemsResponse>>
```

#### TanStack Query Hooks
```typescript
// For client-side data fetching (optional)
export function useAdminPurchasedTemplates(filters)
export function useAdminPurchasedCourses(filters)
```

### Data Table Features

#### Searchable Columns
- Title
- Public ID (Template/Course ID)

#### Filterable Columns
- Category (dynamically populated)
- Creator Type (MyResearch, Instructor)

#### Sortable Columns
- Purchase Date (default descending)
- Title
- Price
- Category

#### Actions Menu
- **Templates**: View Details, Download
- **Courses**: View Details, View Course

### Security & Access Control

- **Admin-only Access**: All endpoints require admin role
- **JWT Authentication**: Proper token-based authentication
- **Role-based Authorization**: Uses existing `@Roles(UserRole.ADMIN)` decorator
- **Error Handling**: Graceful handling of unauthorized access

### Performance Considerations

- **Server-side Pagination**: Reduces initial load time
- **Caching**: 5-minute stale time for TanStack Query
- **Lazy Loading**: Suspense boundaries for progressive loading
- **Optimized Queries**: Efficient database queries with proper indexing

### Future Enhancements

1. **Export Functionality**: CSV/Excel export for purchased items
2. **Advanced Filtering**: Date range filters, price range filters
3. **Bulk Actions**: Bulk operations on selected items
4. **Analytics Dashboard**: Purchase statistics and trends
5. **Real-time Updates**: WebSocket integration for live data
6. **Audit Trail**: Track admin actions on purchased items

### Testing Recommendations

1. **Unit Tests**: Test API functions and utility functions
2. **Integration Tests**: Test page components with mock data
3. **E2E Tests**: Test complete admin workflows
4. **Performance Tests**: Test with large datasets
5. **Security Tests**: Verify admin-only access controls

### Deployment Notes

- Ensure proper environment variables are set
- Verify admin role permissions in production
- Test API endpoints with production data
- Monitor performance with real usage patterns
