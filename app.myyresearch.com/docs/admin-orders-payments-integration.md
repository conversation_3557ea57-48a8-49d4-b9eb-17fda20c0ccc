# Admin Panel Integration for Orders and Payments Management

## Overview

This implementation provides comprehensive admin panel integration for managing orders and payments using the existing `orders` and `payments` API endpoints. The solution follows the established patterns in the codebase and provides separate admin views for orders and payments management.

## Backend Integration

### API Endpoints
- **Orders**: `GET /api/v1/orders/admin/all`
- **Payments**: `GET /api/v1/payments/admin/all`
- **Access Control**: Admin-only with `@UseGuards(JwtAuthGuard, RolesGuard)` and `@Roles(UserRole.ADMIN)`
- **Filtering**: Uses `AdminOrderFilterDto` and `AdminPaymentFilterDto` for comprehensive filtering

### Response Structures
```typescript
interface GetAllOrdersResponse {
  success: boolean;
  message: string;
  orders: Order[];
  total: number;
  page: number;
  totalPages: number;
}

interface GetAllPaymentsResponse {
  success: boolean;
  message: string;
  payments: Payment[];
  total: number;
  page: number;
  totalPages: number;
}
```

## Frontend Implementation

### File Structure
```
app.myyresearch.com/src/
├── types/
│   ├── orders.ts                               # Order types and enums
│   └── payments.ts                             # Payment types and enums
├── lib/
│   ├── orders.ts                               # Orders API integration
│   └── payments.ts                             # Payments API integration
├── hooks/
│   ├── use-orders.ts                           # TanStack Query hooks for orders
│   └── use-payments.ts                         # TanStack Query hooks for payments
├── app/(admin)/orders/
│   ├── page.tsx                               # Orders admin page
│   ├── _components/orders-table.tsx           # Orders data table
│   ├── _lib/queries.ts                        # Server-side queries
│   └── _lib/validations.ts                    # Validation schemas
└── app/(admin)/payments/
    ├── page.tsx                               # Payments admin page
    ├── _components/payments-table.tsx         # Payments data table
    ├── _lib/queries.ts                        # Server-side queries
    └── _lib/validations.ts                     # Validation schemas
```

## Key Features

### 1. Orders Page (`/admin/orders`)
- **Purpose**: Display and manage all orders
- **Data Display**:
  - Order Number
  - Total Amount
  - Order Status (Pending, Processing, Completed, Cancelled, Refunded)
  - Payment Status (Pending, Processing, Completed, Failed, Refunded)
  - Number of Items
  - Created Date
  - Paid Date
- **Actions**: View Details, View Invoice, Update Status

### 2. Payments Page (`/admin/payments`)
- **Purpose**: Display and manage all payments
- **Data Display**:
  - Payment ID (Stripe Payment Intent ID)
  - Order ID
  - Amount (with currency)
  - Payment Status (Pending, Processing, Succeeded, Failed, Cancelled, Refunded, Partially Refunded)
  - Payment Method (Card, Bank Transfer, Wallet)
  - Created Date
  - Paid Date
  - Refund Amount
- **Actions**: View Details, View Receipt, Refund Payment

### 3. Common Features
- **Search**: By order number, payment ID, order ID
- **Filtering**: By status, payment method, date ranges
- **Pagination**: Server-side pagination with configurable page size
- **Sorting**: By creation date (default), amount, status, etc.
- **Loading States**: Skeleton components during data fetching
- **Error Handling**: Proper error boundaries and fallbacks
- **Responsive Design**: Mobile-friendly table layouts

## Technical Implementation

### TypeScript Types

#### Orders
```typescript
export enum OrderStatus {
  PENDING = "pending",
  PROCESSING = "processing",
  COMPLETED = "completed",
  CANCELLED = "cancelled",
  REFUNDED = "refunded",
}

export enum PaymentStatus {
  PENDING = "pending",
  PROCESSING = "processing",
  COMPLETED = "completed",
  FAILED = "failed",
  REFUNDED = "refunded",
}

export interface Order {
  _id: string;
  orderNumber: string;
  userId?: string;
  cartId: string;
  items: OrderItem[];
  subtotal: number;
  discount: number;
  tax: number;
  total: number;
  status: OrderStatus;
  paymentStatus: PaymentStatus;
  // ... additional fields
}
```

#### Payments
```typescript
export enum PaymentStatus {
  PENDING = "pending",
  PROCESSING = "processing",
  SUCCEEDED = "succeeded",
  FAILED = "failed",
  CANCELLED = "cancelled",
  REFUNDED = "refunded",
  PARTIALLY_REFUNDED = "partially_refunded",
}

export interface Payment {
  _id: string;
  userId?: string;
  orderId: string;
  cartId: string;
  stripePaymentIntentId: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  paymentMethod?: PaymentMethod;
  // ... additional fields
}
```

### API Integration
```typescript
// Orders API functions
export async function getAdminOrders(params: AdminOrderFilter)
export async function getAdminOrderById(orderId: string)
export async function updateOrderStatus(orderId: string, status: string)

// Payments API functions
export async function getAdminPayments(params: AdminPaymentFilter)
export async function getAdminPaymentById(paymentId: string)
export async function refundPayment(paymentId: string, amount?: number, reason?: string)
```

### TanStack Query Hooks
```typescript
// Orders hooks
export function useAdminOrders(filters: AdminOrderFilter)
export function useAdminOrderById(orderId: string)
export function useUpdateOrderStatus()

// Payments hooks
export function useAdminPayments(filters: AdminPaymentFilter)
export function useAdminPaymentById(paymentId: string)
export function useRefundPayment()
```

## Data Table Features

### Orders Table
#### Searchable Columns
- Order Number

#### Filterable Columns
- Order Status (Pending, Processing, Completed, Cancelled, Refunded)
- Payment Status (Pending, Processing, Completed, Failed, Refunded)

#### Sortable Columns
- Created Date (default descending)
- Total Amount
- Order Number
- Status

### Payments Table
#### Searchable Columns
- Payment ID (Stripe Payment Intent ID)
- Order ID

#### Filterable Columns
- Payment Status (Pending, Processing, Succeeded, Failed, Cancelled, Refunded, Partially Refunded)
- Payment Method (Card, Bank Transfer, Wallet)

#### Sortable Columns
- Created Date (default descending)
- Amount
- Payment Status

## Security & Access Control

- **Admin-only Access**: All endpoints require admin role
- **JWT Authentication**: Proper token-based authentication
- **Role-based Authorization**: Uses existing `@Roles(UserRole.ADMIN)` decorator
- **Error Handling**: Graceful handling of unauthorized access

## Performance Considerations

- **Server-side Pagination**: Reduces initial load time
- **Caching**: 5-minute stale time for TanStack Query
- **Lazy Loading**: Suspense boundaries for progressive loading
- **Optimized Queries**: Efficient database queries with proper indexing

## Future Enhancements

1. **Export Functionality**: CSV/Excel export for orders and payments
2. **Advanced Analytics**: Revenue analytics, payment method statistics
3. **Bulk Actions**: Bulk status updates, bulk refunds
4. **Real-time Updates**: WebSocket integration for live data
5. **Audit Trail**: Track admin actions on orders and payments
6. **Advanced Filtering**: Customer filters, amount range filters
7. **Order Details Modal**: Detailed order view with items breakdown
8. **Payment Receipt Generation**: Automated receipt generation and email

## Testing Recommendations

1. **Unit Tests**: Test API functions and utility functions
2. **Integration Tests**: Test page components with mock data
3. **E2E Tests**: Test complete admin workflows
4. **Performance Tests**: Test with large datasets
5. **Security Tests**: Verify admin-only access controls

## Deployment Notes

- Ensure proper environment variables are set
- Verify admin role permissions in production
- Test API endpoints with production data
- Monitor performance with real usage patterns
- Set up proper error tracking and logging
