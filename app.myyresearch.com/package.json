{"name": "e9-next-jwt-admin-template", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-s3": "^3.741.0", "@aws-sdk/cloudfront-signer": "^3.734.0", "@aws-sdk/s3-presigned-post": "^3.741.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@faker-js/faker": "^9.3.0", "@hello-pangea/dnd": "^17.0.0", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@sanity/vision": "^3.74.1", "@t3-oss/env-nextjs": "^0.11.1", "@tanstack/react-query": "^5.80.7", "@tanstack/react-table": "^8.20.5", "@tiptap/extension-blockquote": "^2.11.5", "@tiptap/extension-heading": "^2.11.5", "@tiptap/extension-horizontal-rule": "^2.11.5", "@tiptap/extension-image": "^2.11.5", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-subscript": "^2.11.5", "@tiptap/extension-superscript": "^2.11.5", "@tiptap/extension-task-item": "^2.11.5", "@tiptap/extension-task-list": "^2.11.5", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/extension-text-style": "^2.11.5", "@tiptap/extension-underline": "^2.11.5", "@tiptap/extension-youtube": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@types/js-cookie": "^3.0.6", "@types/react-beautiful-dnd": "^13.1.8", "@uppy/aws-s3": "^4.2.3", "@uppy/core": "^4.4.2", "@uppy/react": "^4.2.1", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "immer": "^10.1.1", "input-otp": "^1.4.1", "jose": "^5.9.6", "js-cookie": "^3.0.5", "lucide-react": "^0.468.0", "next": "15.1.0", "next-sanity": "^9.8.55", "next-themes": "^0.4.4", "nextjs-toploader": "^3.7.15", "nuqs": "^2.2.3", "react": "^19.0.0", "react-beautiful-dnd": "^13.1.1", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.14.1", "sanity": "^3.74.1", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.5", "vaul": "^1.1.1", "zod": "^3.24.1", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}