import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Add the following configuration

  typescript: {
    ignoreBuildErrors: true,
  },

  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname:
          process.env.AWS_FILES_CLOUDFRONT_URL?.replace(/^https?:\/\//, "") ||
          "",
      },
      {
        protocol: "https",
        hostname:
          process.env.AWS_IMAGES_CLOUDFRONT_URL?.replace(/^https?:\/\//, "") ||
          "",
      },
      {
        protocol: "https",
        hostname:
          process.env.AWS_VIDEOS_CLOUDFRONT_URL?.replace(/^https?:\/\//, "") ||
          "",
      },
      {
        protocol: "https",
        hostname:
          process.env.NEXT_PUBLIC_AWS_FILES_CLOUDFRONT_URL?.replace(
            /^https?:\/\//,
            ""
          ) || "",
      },
      {
        protocol: "https",
        hostname:
          process.env.NEXT_PUBLIC_AWS_IMAGES_CLOUDFRONT_URL?.replace(
            /^https?:\/\//,
            ""
          ) || "",
      },
      {
        protocol: "https",
        hostname:
          process.env.NEXT_PUBLIC_AWS_VIDEOS_CLOUDFRONT_URL?.replace(
            /^https?:\/\//,
            ""
          ) || "",
      },
    ],
  },
};

export default nextConfig;
